import type { ModuleOptions } from '@nuxtjs/sitemap'

// https://nuxt.com/docs/api/configuration/nuxt-config

export default defineNuxtConfig({
  
  devtools: { enabled: true },
  css: ['~/assets/css/globals.css', '@/assets/css/animations.css'],
  postcss: {
    plugins: {
      'tailwindcss/nesting': {},
      tailwindcss: {},
      autoprefixer: {},
    },
  },
  runtimeConfig: {
    // secret keys, only available on server
    openaiApiKey: process.env.OPENAI_API_KEY,
    supabaseUrl: process.env.SUPABASE_URL,
    supabaseServiceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
    stripeServerKey: process.env.STRIPE_SERVER_KEY,
    // public keys, also exposed to client
    public: {
      stripeClientKey: process.env.STRIPE_CLIENT_KEY,
      publicUrl: process.env.PUBLIC_URL,
      EXTENSION_ID: process.env.NUXT_PUBLIC_EXTENSION_ID,
      CHROME_EXTENSION_ID: process.env.CHROME_EXTENSION_ID,
      apiBase: process.env.NUXT_PUBLIC_API_BASE,
      supabaseUrl: process.env.SUPABASE_URL,
      supabaseAnonKey: process.env.SUPABASE_ANON_KEY,
    }
  },

  nitro: {
    routeRules: {
      // Proxy API routes to external service, but exclude Nuxt Content routes
      '/api/create-checkout-session': {
        proxy: process.env.NUXT_PUBLIC_API_BASE ? `${process.env.NUXT_PUBLIC_API_BASE}/api/create-checkout-session` : 'https://fillify-343190162770.asia-east1.run.app/api/create-checkout-session',
        cors: true,
        headers: {
          'Access-Control-Allow-Methods': 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': '*',
          'Access-Control-Allow-Credentials': 'true',
          'Access-Control-Max-Age': '86400'
        }
      },
      '/api/update-credits': {
        proxy: process.env.NUXT_PUBLIC_API_BASE ? `${process.env.NUXT_PUBLIC_API_BASE}/api/update-credits` : 'https://fillify-343190162770.asia-east1.run.app/api/update-credits',
        cors: true,
        headers: {
          'Access-Control-Allow-Methods': 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': '*',
          'Access-Control-Allow-Credentials': 'true',
          'Access-Control-Max-Age': '86400'
        }
      }
      // Note: We removed the catch-all '/api/**' rule to allow Nuxt Content API to work
    }
  },

  compatibilityDate: '2024-08-19',
  modules: [
    '@nuxtjs/sitemap',
    "@unlok-co/nuxt-stripe",  
    '@nuxtjs/i18n',
    '@nuxt/content',
  ],
  sitemap: <Partial<ModuleOptions>>{
    hostname: 'https://fillify.tech',
    gzip: true,
    autoI18n: true,
    exclude: [
      '/admin/**',
      '/api/**',
      '/refund',
      '/links',
      '/success',
      '/cancel',
      '/price',
      '/contact',
      '/signin',
      '/dashboard'
    ],
    defaults: {
      changefreq: 'weekly',
      priority: 0.5
    },
    sources: [
      {
        fetch: '/__sitemap__/blog'
      }
    ]
  },
  stripe: {
    // Server
    server: {
      key: process.env.STRIPE_SERVER_KEY,
      options: {
        maxNetworkRetries: 1,
        telemetry: true,
        // https://github.com/stripe/stripe-node?tab=readme-ov-file#configuration
      },
      // CLIENT
    },
    client: {
      key: process.env.STRIPE_CLIENT_KEY,
      // your api options override for stripe client side https://stripe.com/docs/js/initializing#init_stripe_js-options
      options: {},
    },
  },
  site: {
    url: 'https://fillify.tech', // Change to your site URL
  },
  i18n: {
    vueI18n: './i18n.config.ts',
    locales: [
      { code: 'en', language: 'en-US', file: 'en.ts', name: 'English', nativeName: 'English' },
      { code: 'zh', language: 'zh-CN', file: 'zh.ts', name: 'Chinese', nativeName: '简体中文' },
      { code: 'zh-TW', language: 'zh-TW', file: 'zh-TW.ts', name: 'Traditional Chinese', nativeName: '繁體中文' },
      { code: 'es', language: 'es-ES', file: 'es.ts', name: 'Spanish', nativeName: 'Español' },
      { code: 'fr', language: 'fr-FR', file: 'fr.ts', name: 'French', nativeName: 'Français' },
      { code: 'ko', language: 'ko-KR', file: 'ko.ts', name: 'Korean', nativeName: '한국어' },
      { code: 'ja', language: 'ja-JP', file: 'ja.ts', name: 'Japanese', nativeName: '日本語' },
      { code: 'ru', language: 'ru-RU', file: 'ru.ts', name: 'Russian', nativeName: 'Русский' }
    ],
    defaultLocale: 'en',
    strategy: 'prefix_and_default',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'fillify-lang',
      fallbackLocale: 'en',
      redirectOn: 'root'
    },
    langDir: 'locales/',
    lazy: true,
  },
  app: {
    head: {
      htmlAttrs: {
        lang: 'en'
      },
      title: 'Fillify - AI-Powered Forms, Emails & Bug Reports Assistant',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { 
          name: 'description', 
          content: 'Fillify revolutionizes form filling with AI technology. Automatically complete web forms, compose emails, and generate bug reports with intelligent automation.'
        },
        {
          name: 'keywords',
          content: 'AI Form Filling, AI Automation, AI Email Generation, AI Bug Report Generation, Smart Form Completion, Automated Data Entry, AI Form Assistant, Intelligent Form Filling, Chrome Form Autofill, AI Form Filler'
        },
        // Primary Open Graph
        { property: 'og:type', content: 'website' },
        { property: 'og:url', content: 'https://fillify.tech' },
        { property: 'og:site_name', content: 'Fillify' },
        { 
          property: 'og:title', 
          content: 'Fillify - Transform Your Form Filling with AI' 
        },
        { 
          property: 'og:description', 
          content: 'Experience the future of form filling with Fillify. Our AI-powered solution automatically fills forms, generates reports, and streamlines your workflow - saving you hours of manual work.' 
        },
        { 
          property: 'og:image', 
          content: 'https://fillify.tech/og/og-image.png' 
        },
        { property: 'og:image:width', content: '1200' },
        { property: 'og:image:height', content: '630' },
        { 
          property: 'og:image:alt', 
          content: 'Fillify - AI-Powered Form Automation Platform' 
        },
        { property: 'og:locale', content: 'en_US' },
        
        // Twitter Card
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:site', content: '@fillify' },
        { name: 'twitter:creator', content: '@fillify' },
        { 
          name: 'twitter:title', 
          content: 'Fillify - Transform Your Form Filling with AI' 
        },
        { 
          name: 'twitter:description', 
          content: 'Experience the future of form filling with Fillify. Our AI-powered solution automatically fills forms, generates reports, and streamlines your workflow.' 
        },
        { 
          name: 'twitter:image', 
          content: 'https://fillify.tech/og/og-image.png' 
        },
        { 
          name: 'twitter:image:alt', 
          content: 'Fillify - AI-Powered Form Automation Platform' 
        },

        // LinkedIn
        { property: 'linkedin:card', content: 'summary_large_image' },
        { 
          property: 'linkedin:title', 
          content: 'Fillify - Transform Your Form Filling with AI' 
        },
        { 
          property: 'linkedin:description', 
          content: 'Experience the future of form filling with Fillify. Our AI-powered solution automatically fills forms, generates reports, and streamlines your workflow.' 
        },
        { 
          property: 'linkedin:image', 
          content: 'https://fillify.tech/og/og-image.png' 
        },
      ],
      link: [
        // Favicon
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'icon', type: 'image/svg+xml', href: '/logo/Fillify-Logo.svg' },
        { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/logo/logo-32.png' },
        { rel: 'icon', type: 'image/png', sizes: '64x64', href: '/logo/logo-64.png' },
        // Apple Touch Icon
        { rel: 'apple-touch-icon', sizes: '180x180', href: '/logo/logo-512.png' },
        // Canonical URL
        { rel: 'canonical', href: 'https://fillify.tech', key: 'canonical' }
      ],
      script: [
        { 
          src: 'https://analytics.ahrefs.com/analytics.js', 
          'data-key': '+/J6q8Q5DWjKfKuL3uzjdg',
          async: true 
        }
      ]
    }
  },
  imports: {
    dirs: ['composables/**'],
  },

  components: {
    dirs: [
      {
        path: '~/components',
        pathPrefix: false,
      },
    ],
  },

  build: {
    transpile: ['lucide-vue-next', 'gsap'],
  },
})
