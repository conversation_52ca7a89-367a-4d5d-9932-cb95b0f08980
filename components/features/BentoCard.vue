<template>
  <div 
    class="group relative overflow-hidden rounded-3xl bg-white border border-gray-200 shadow-sm hover:shadow-xl transition-all duration-500"
    :class="[
      spanClasses,
      { 'hover:-translate-y-1': true }
    ]"
  >
    <!-- Background Gradient/Pattern -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-50/50 to-white opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
    
    <div class="relative h-full flex flex-col">
      <!-- Content -->
      <div class="p-8 z-10">
        <div class="mb-4 inline-flex items-center justify-center w-12 h-12 rounded-xl bg-blue-50 text-blue-600 group-hover:scale-110 group-hover:rotate-3 transition-transform duration-300">
          <component :is="icon" class="w-6 h-6" />
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
          {{ title }}
        </h3>
        <p class="text-gray-500 leading-relaxed">
          {{ description }}
        </p>
      </div>

      <!-- Demo/Image Area -->
      <div class="flex-1 relative min-h-[200px] mt-4 overflow-hidden bg-gray-50/50 border-t border-gray-100">
        <div class="absolute inset-0 flex items-center justify-center p-6 group-hover:scale-[1.02] transition-transform duration-500">
          <component
            v-if="demoComponent"
            :is="demoComponent"
            class="w-full h-full max-h-[300px]"
          />
          <img
            v-else-if="image"
            :src="image"
            :alt="title"
            class="w-full h-full object-contain"
            loading="lazy"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Component } from 'vue'

interface Props {
  icon: Component
  title: string
  description: string
  image?: string
  demoComponent?: Component
  span?: '1' | '2' | '3'
}

const props = withDefaults(defineProps<Props>(), {
  span: '1',
  image: '',
  demoComponent: undefined
})

const spanClasses = computed(() => {
  switch (props.span) {
    case '2': return 'md:col-span-2'
    case '3': return 'md:col-span-3'
    default: return 'md:col-span-1'
  }
})
</script>
