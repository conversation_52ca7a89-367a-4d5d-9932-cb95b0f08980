<template>
  <div class="feature-section" ref="featureSectionRef">
    <div class="container mx-auto px-4 py-8 md:py-12">
      <div class="feature-card">
        <div class="flex flex-col lg:flex-row items-center gap-8 md:gap-12 lg:gap-16" :class="{ 'lg:flex-row-reverse': reverse }">
          <!-- Demo/Image Section -->
          <div class="w-full lg:w-1/2">
            <div class="demo-wrapper">
              <!-- Use demo component if provided, otherwise fallback to image -->
              <component
                v-if="demoComponent"
                :is="demoComponent"
                class="w-full h-full"
              />
              <img
                v-else
                :src="image"
                :alt="t(`features.${translationKey}.alt`)"
                class="w-full h-full object-contain"
                loading="lazy"
              />
            </div>
          </div>

          <!-- Content Section -->
          <div class="w-full lg:w-1/2 space-y-6">
            <div class="icon-wrapper">
              <component :is="icon" class="icon" />
            </div>
            <h3 class="feature-title">
              {{ title }}
            </h3>
            <p class="feature-description">
              {{ description }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Component } from 'vue'
import { ref } from 'vue'
import { useIntersectionObserver } from '@vueuse/core'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

interface Props {
  icon: Component
  title: string
  description: string
  image?: string
  demoComponent?: Component
  reverse?: boolean
  translationKey: string
}

withDefaults(defineProps<Props>(), {
  reverse: false,
  image: '',
  demoComponent: undefined
})

const featureSectionRef = ref<HTMLElement | null>(null)

// 设置交叉观察器
useIntersectionObserver(featureSectionRef, ([{ isIntersecting }]) => {
  if (isIntersecting && featureSectionRef.value) {
    featureSectionRef.value.classList.add('animate-feature')
  }
})
</script>

<style scoped>
.feature-section {
  position: relative;
  opacity: 0;
  transform: translateY(30px);
}

.feature-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.06);
  padding: 3rem 2rem;
  transition: all 0.3s ease;
}

.feature-card:hover {
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

@media (min-width: 768px) {
  .feature-card {
    padding: 4rem 3rem;
  }
}

@media (min-width: 1024px) {
  .feature-card {
    padding: 5rem 4rem;
  }
}

/* Demo wrapper */
.demo-wrapper {
  position: relative;
  width: 100%;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  overflow: hidden;
  background: rgba(249, 250, 251, 0.5);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(229, 231, 235, 0.5);
}

@media (max-width: 768px) {
  .demo-wrapper {
    height: 400px;
  }
}

@media (max-width: 640px) {
  .demo-wrapper {
    height: 350px;
  }
}

/* Icon wrapper */
.icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transition: all 0.3s ease;
}

.icon-wrapper:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);
}

.icon {
  width: 28px;
  height: 28px;
  color: #2563eb;
}

/* Typography */
.feature-title {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.3;
  color: #111827;
}

@media (min-width: 768px) {
  .feature-title {
    font-size: 1.875rem;
  }
}

.feature-description {
  font-size: 1.125rem;
  line-height: 1.75;
  color: #6b7280;
}

/* Animation */
.feature-section.animate-feature {
  animation: feature-fade-in 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes feature-fade-in {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 