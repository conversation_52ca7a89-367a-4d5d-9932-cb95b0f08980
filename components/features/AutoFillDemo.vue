<template>
  <div class="demo-container">
    <div class="demo-content">
      <!-- Forms Container -->
      <div class="demo-card">
        <!-- General Form -->
        <div v-if="currentFormType === 'general'" class="space-y-2.5" key="general">
          <div class="form-field" :class="{ 'filling': fillingFields.includes('name') }">
            <label class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
            <input
              type="text"
              :value="formData.general.name"
              readonly
              class="form-input w-full px-3.5 py-2.5 border border-gray-300 rounded-lg text-sm"
            />
          </div>

          <div class="form-field" :class="{ 'filling': fillingFields.includes('email') }">
            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              type="email"
              :value="formData.general.email"
              readonly
              class="form-input w-full px-3.5 py-2.5 border border-gray-300 rounded-lg text-sm"
            />
          </div>

          <div class="form-field" :class="{ 'filling': fillingFields.includes('experience') }">
            <label class="block text-sm font-medium text-gray-700 mb-1">Work Experience</label>
            <textarea
              :value="formData.general.experience"
              readonly
              class="form-input w-full px-3.5 py-2.5 border border-gray-300 rounded-lg resize-none h-24 text-sm leading-relaxed"
            ></textarea>
          </div>
        </div>

        <!-- Email Form -->
        <div v-if="currentFormType === 'email'" class="space-y-2.5" key="email">
          <div class="form-field" :class="{ 'filling': fillingFields.includes('to') }">
            <label class="block text-sm font-medium text-gray-700 mb-1">To</label>
            <input
              type="email"
              :value="formData.email.to"
              readonly
              class="form-input w-full px-3.5 py-2.5 border border-gray-300 rounded-lg text-sm"
            />
          </div>

          <div class="form-field" :class="{ 'filling': fillingFields.includes('subject') }">
            <label class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
            <input
              type="text"
              :value="formData.email.subject"
              readonly
              class="form-input w-full px-3.5 py-2.5 border border-gray-300 rounded-lg text-sm"
            />
          </div>

          <div class="form-field" :class="{ 'filling': fillingFields.includes('body') }">
            <label class="block text-sm font-medium text-gray-700 mb-1">Message</label>
            <textarea
              :value="formData.email.body"
              readonly
              class="form-input w-full px-3.5 py-2.5 border border-gray-300 rounded-lg resize-none h-52 text-sm leading-relaxed"
            ></textarea>
          </div>
        </div>

        <!-- Bug Report Form -->
        <div v-if="currentFormType === 'bugReport'" class="space-y-2.5" key="bugReport">
          <div class="form-field" :class="{ 'filling': fillingFields.includes('title') }">
            <label class="block text-sm font-medium text-gray-700 mb-1">Title</label>
            <input
              type="text"
              :value="formData.bugReport.title"
              readonly
              class="form-input w-full px-3.5 py-2.5 border border-gray-300 rounded-lg text-sm"
            />
          </div>

          <div class="form-field" :class="{ 'filling': fillingFields.includes('description') }">
            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              :value="formData.bugReport.description"
              readonly
              class="form-input w-full px-3.5 py-2.5 border border-gray-300 rounded-lg resize-none h-20 text-sm leading-relaxed"
            ></textarea>
          </div>

          <div class="form-field" :class="{ 'filling': fillingFields.includes('steps') }">
            <label class="block text-sm font-medium text-gray-700 mb-1">Steps to Reproduce</label>
            <textarea
              :value="formData.bugReport.steps"
              readonly
              class="form-input w-full px-3.5 py-2.5 border border-gray-300 rounded-lg resize-none h-40 text-sm leading-relaxed"
            ></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

type FormType = 'general' | 'email' | 'bugReport'

const currentFormType = ref<FormType>('general')
const fillingFields = ref<string[]>([])

const formData = ref({
  general: {
    name: '',
    email: '',
    experience: ''
  },
  email: {
    to: '',
    subject: '',
    body: ''
  },
  bugReport: {
    title: '',
    description: '',
    steps: ''
  }
})

const formTemplates = {
  general: {
    name: 'John Anderson',
    email: '<EMAIL>',
    experience: 'Senior Software Engineer at Tech Corp with 5+ years experience in full-stack development.'
  },
  email: {
    to: '<EMAIL>',
    subject: 'Project Timeline Update',
    body: 'Hi,\n\nUpdate on Q4 deliverables: First milestone Oct 15th, final release Nov 30th.\n\nBest,\nJohn'
  },
  bugReport: {
    title: 'Login Failure After Password Reset',
    description: 'Users cannot login after password reset. "Invalid credentials" error appears.',
    steps: '1. Go to login\n2. Click "Forgot Password"\n3. Reset via email\n4. Try to login\n5. Error occurs'
  }
}

const fillForm = async (type: FormType) => {
  fillingFields.value = []

  // Reset form data
  if (type === 'general') {
    formData.value.general = { name: '', email: '', experience: '' }
    const fields: (keyof typeof formTemplates.general)[] = ['name', 'email', 'experience']

    for (const field of fields) {
      fillingFields.value.push(field)
      await new Promise(resolve => setTimeout(resolve, 400))

      const text = formTemplates.general[field]
      for (let i = 0; i <= text.length; i++) {
        formData.value.general[field] = text.substring(0, i)
        await new Promise(resolve => setTimeout(resolve, 10))
      }
      await new Promise(resolve => setTimeout(resolve, 300))
    }
  } else if (type === 'email') {
    formData.value.email = { to: '', subject: '', body: '' }
    const fields: (keyof typeof formTemplates.email)[] = ['to', 'subject', 'body']

    for (const field of fields) {
      fillingFields.value.push(field)
      await new Promise(resolve => setTimeout(resolve, 400))

      const text = formTemplates.email[field]
      for (let i = 0; i <= text.length; i++) {
        formData.value.email[field] = text.substring(0, i)
        await new Promise(resolve => setTimeout(resolve, 10))
      }
      await new Promise(resolve => setTimeout(resolve, 300))
    }
  } else if (type === 'bugReport') {
    formData.value.bugReport = { title: '', description: '', steps: '' }
    const fields: (keyof typeof formTemplates.bugReport)[] = ['title', 'description', 'steps']

    for (const field of fields) {
      fillingFields.value.push(field)
      await new Promise(resolve => setTimeout(resolve, 400))

      const text = formTemplates.bugReport[field]
      for (let i = 0; i <= text.length; i++) {
        formData.value.bugReport[field] = text.substring(0, i)
        await new Promise(resolve => setTimeout(resolve, 10))
      }
      await new Promise(resolve => setTimeout(resolve, 300))
    }
  }

  // Wait before switching to next form
  await new Promise(resolve => setTimeout(resolve, 2500))
  switchToNextForm()
}

const switchToNextForm = () => {
  const types: FormType[] = ['general', 'email', 'bugReport']
  const currentIndex = types.indexOf(currentFormType.value)
  const nextIndex = (currentIndex + 1) % types.length
  currentFormType.value = types[nextIndex]
  fillForm(types[nextIndex])
}

let animationTimeout: NodeJS.Timeout | null = null

onMounted(() => {
  fillForm(currentFormType.value)
})

onUnmounted(() => {
  if (animationTimeout) clearTimeout(animationTimeout)
})
</script>

<style scoped>
.demo-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ECFDF5 0%, #D1FAE5 100%);
  border-radius: 20px;
  padding: clamp(1rem, 2.5vw, 2rem);
}

.demo-content {
  width: 100%;
  max-width: 28rem;
  font-size: clamp(0.8rem, 2vw, 1rem);
}

.demo-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: clamp(0.75rem, 2vw, 1.25rem);
  display: flex;
  flex-direction: column;
  gap: clamp(0.4rem, 1.2vw, 0.625rem);
}

.form-input {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: clamp(0.8rem, 1.8vw, 1rem);
  background-color: #fff;
  color: #333;
  padding: clamp(0.5rem, 1.5vw, 0.625rem) clamp(0.75rem, 2vw, 0.875rem);
  border: 1px solid #d1d5db;
  border-radius: clamp(4px, 1.2vw, 8px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.form-input:focus {
  outline: none;
  border-color: #2962FF;
  box-shadow: 0 0 0 3px rgba(41, 98, 255, 0.1);
}

.form-field {
  transition: all 0.3s ease;
}

.form-field.filling {
  position: relative;
}

.form-field.filling .form-input {
  border-color: #2962FF;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  animation: field-glow 1.5s ease-in-out;
}

@keyframes field-glow {
  0%, 100% {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  50% {
    box-shadow: 0 0 0 5px rgba(59, 130, 246, 0.2);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-input {
    padding: 0.5rem 0.75rem;
  }
}

@media (max-width: 640px) {
  .form-input {
    padding: 0.4rem 0.6rem;
    font-size: 0.85rem;
  }
}
</style>
