<template>
  <div class="demo-container">
    <div class="demo-content">
      <div class="network-container-wrapper">
        <div class="network-container" ref="containerRef">
          <!-- Connection lines using CSS -->
          <div
            v-for="(provider, index) in providers"
            :key="`line-${provider.id}`"
            class="connection-line"
            :class="{ 'active': activeProvider === index }"
            :style="getLineStyle(index)"
          >
          </div>

          <!-- Center Node - Fillify Logo -->
          <div class="center-node">
            <img
              src="/logo/logo-512.png"
              alt="Fillify"
              class="fillify-logo"
            />
          </div>

          <!-- Provider Nodes -->
          <div
            v-for="(provider, index) in providers"
            :key="provider.id"
            class="provider-node"
            :class="{ 'active': activeProvider === index }"
            :style="getNodeStyle(index)"
            @mouseenter="activeProvider = index"
            @mouseleave="activeProvider = -1"
          >
            <div class="node-bubble">
              <img
                :src="`/images/providers/${provider.id}.png`"
                :alt="provider.name"
                class="provider-logo"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

interface Provider {
  name: string
  id: string
}

const providers: Provider[] = [
  { name: 'OpenAI', id: 'openai' },
  { name: 'Claude', id: 'claude' },
  { name: 'Gemini', id: 'gemini' },
  { name: 'Ollama', id: 'ollama' },
  { name: 'Moonshot', id: 'moonshot' },
  { name: 'Deepseek', id: 'deepseek' },
  { name: 'OpenRouter', id: 'openrouter' }
]

const activeProvider = ref(-1)
const containerRef = ref<HTMLElement | null>(null)

// Calculate position for each provider node on a circle, relative to container size
const getProviderPosition = (index: number) => {
  // Default values for original 400x400 container
  const originalCenterX = 200
  const originalCenterY = 200
  const originalRadius = 140
  
  let centerX, centerY, radius
  
  if (!containerRef.value) {
    // If container ref isn't available yet, use original values
    centerX = originalCenterX
    centerY = originalCenterY
    radius = originalRadius
  } else {
    // Calculate based on actual container size
    const actualWidth = containerRef.value.clientWidth
    const actualHeight = containerRef.value.clientHeight
    
    // Calculate center
    centerX = actualWidth / 2
    centerY = actualHeight / 2
    
    // Calculate radius proportionally to smaller dimension
    const actualSize = Math.min(actualWidth, actualHeight)
    const scaleFactor = actualSize / 400  // 400 is original container size
    radius = originalRadius * scaleFactor
  }
  
  const total = providers.length
  const angle = (index * 360) / total - 90 // Start from top

  const x = centerX + radius * Math.cos((angle * Math.PI) / 180)
  const y = centerY + radius * Math.sin((angle * Math.PI) / 180)

  return { x, y }
}

const getNodeStyle = (index: number) => {
  const pos = getProviderPosition(index)
  return {
    left: `${pos.x}px`,
    top: `${pos.y}px`
  }
}

const getLineStyle = (index: number) => {
  const pos = getProviderPosition(index)
  
  let centerX, centerY
  if (!containerRef.value) {
    centerX = 200
    centerY = 200
  } else {
    centerX = containerRef.value.clientWidth / 2
    centerY = containerRef.value.clientHeight / 2
  }

  // 计算线条的长度和角度
  const deltaX = pos.x - centerX
  const deltaY = pos.y - centerY
  const length = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
  const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI)

  return {
    left: `${centerX}px`,
    top: `${centerY}px`,
    width: `${length}px`,
    transform: `rotate(${angle}deg)`,
    transformOrigin: '0 50%'
  }
}

// Recalculate positions when needed
const recalculatePositions = () => {
  // This forces Vue to recompute the styles by triggering a reactive update
  activeProvider.value = activeProvider.value
}

// Auto-rotate active provider
let intervalId: NodeJS.Timeout
let resizeObserver: ResizeObserver | null = null

const startAutoRotate = () => {
  intervalId = setInterval(() => {
    activeProvider.value = (activeProvider.value + 1) % providers.length
  }, 2000)
}

onMounted(() => {
  // 立即激活第一个提供商（OpenAI）以显示连接线
  activeProvider.value = 0
  
  // Set up resize observer to handle container size changes
  if (containerRef.value) {
    resizeObserver = new ResizeObserver(() => {
      recalculatePositions()
    })
    resizeObserver.observe(containerRef.value)
  }
  
  startAutoRotate()
})

onUnmounted(() => {
  if (intervalId) clearInterval(intervalId)
  if (resizeObserver) resizeObserver.disconnect()
})
</script>

<style scoped>
.demo-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #F5F3FF 0%, #EDE9FE 100%);
  border-radius: 20px;
  padding: 2rem;
}

.demo-content {
  transform-origin: center;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (max-width: 768px) {
  .demo-content {
    transform: scale(0.85);
  }
}

@media (max-width: 640px) {
  .demo-content {
    transform: scale(0.75);
  }
}

.network-container-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 400px;
  aspect-ratio: 1;
  margin: 0 auto;
}

.network-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-width: 200px; /* Minimum size to ensure usability */
  min-height: 200px;
}

/* CSS Connection Lines */
.connection-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg,
    rgba(99, 102, 241, 0.8) 0%,
    rgba(99, 102, 241, 0.4) 50%,
    rgba(99, 102, 241, 0.1) 100%
  );
  opacity: 0.3;
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 0;
}

.connection-line.active {
  opacity: 1;
  background: linear-gradient(90deg,
    rgba(99, 102, 241, 1) 0%,
    rgba(99, 102, 241, 0.8) 50%,
    rgba(99, 102, 241, 0.3) 100%
  );
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.4);
}




/* Center Node */
.center-node {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.fillify-logo {
  position: relative;
  width: 60px;
  height: 60px;
  object-fit: contain;
  z-index: 2;
  filter: drop-shadow(0 4px 16px rgba(99, 102, 241, 0.3));
}

/* Provider Nodes */
.provider-node {
  position: absolute;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  cursor: default;
  pointer-events: none;
  z-index: 2;
}

.node-bubble {
  width: 60px;
  height: 60px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.provider-node:hover .node-bubble,
.provider-node.active .node-bubble {
  transform: scale(1.15);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.25);
}

.provider-logo {
  width: 35px;
  height: 35px;
  object-fit: contain;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .network-container {
    width: 300px;
    height: 300px;
  }

  .fillify-logo {
    width: 50px;
    height: 50px;
  }

  .node-bubble {
    width: 50px;
    height: 50px;
  }

  .provider-logo {
    width: 28px;
    height: 28px;
  }
}

@media (max-width: 640px) {
  .network-container {
    width: 250px;
    height: 250px;
  }

  .fillify-logo {
    width: 40px;
    height: 40px;
  }

  .node-bubble {
    width: 40px;
    height: 40px;
  }

  .provider-logo {
    width: 22px;
    height: 22px;
  }
}
</style>