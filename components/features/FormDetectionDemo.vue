<template>
  <div class="demo-container">
    <div class="demo-content">
      <!-- White container card -->
      <div class="demo-card">
        <!-- First Row - Two fields side by side -->
        <div class="grid grid-cols-2 gap-2.5">
        <div
          class="relative"
          :class="{ 'field-detected': detectedFields.includes(0) }"
        >
          <input
            type="text"
            class="form-input w-full px-3.5 py-2.5 rounded-lg border border-gray-300 focus:outline-none transition-all duration-300 text-sm"
            placeholder="First Name"
            readonly
          />
        </div>
        <div
          class="relative"
          :class="{ 'field-detected': detectedFields.includes(1) }"
        >
          <input
            type="text"
            class="form-input w-full px-3.5 py-2.5 rounded-lg border border-gray-300 focus:outline-none transition-all duration-300 text-sm"
            placeholder="Last Name"
            readonly
          />
        </div>
      </div>

      <!-- Second Row - Two fields side by side -->
      <div class="grid grid-cols-2 gap-2.5">
        <div
          class="relative"
          :class="{ 'field-detected': detectedFields.includes(2) }"
        >
          <input
            type="text"
            class="form-input w-full px-3.5 py-2.5 rounded-lg border border-gray-300 focus:outline-none transition-all duration-300 text-sm"
            placeholder="Email Address"
            readonly
          />
        </div>
        <div
          class="relative"
          :class="{ 'field-detected': detectedFields.includes(3) }"
        >
          <input
            type="text"
            class="form-input w-full px-3.5 py-2.5 rounded-lg border border-gray-300 focus:outline-none transition-all duration-300 text-sm"
            placeholder="Phone Number"
            readonly
          />
        </div>
      </div>

      <!-- Third Row - One wide textarea -->
      <div
        class="relative"
        :class="{ 'field-detected': detectedFields.includes(4) }"
      >
        <textarea
          class="form-input w-full px-3.5 py-2.5 rounded-lg border border-gray-300 focus:outline-none transition-all duration-300 resize-none text-sm"
          placeholder="Additional Notes"
          rows="3"
          readonly
        ></textarea>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const detectedFields = ref<number[]>([])
let detectionInterval: NodeJS.Timeout | null = null

const detectFields = () => {
  let currentIndex = 0

  const showNext = () => {
    if (currentIndex < 5) {
      // Highlight current field
      detectedFields.value = [currentIndex]
      currentIndex++

      // Schedule next field (consistent timing)
      detectionInterval = setTimeout(showNext, 800)
    } else {
      // Clear all and restart after a brief pause
      detectedFields.value = []
      setTimeout(() => {
        currentIndex = 0
        detectionInterval = setTimeout(showNext, 800)
      }, 500)
    }
  }

  // Start the animation
  detectionInterval = setTimeout(showNext, 500)
}

onMounted(() => {
  detectFields()
})

onUnmounted(() => {
  if (detectionInterval) {
    clearTimeout(detectionInterval)
  }
})
</script>

<style scoped>
.demo-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #EBF4FF 0%, #E0E7FF 100%);
  border-radius: 20px;
  padding: clamp(1rem, 2.5vw, 2rem);
}

.demo-content {
  width: 100%;
  max-width: 28rem;
  font-size: clamp(0.8rem, 2vw, 1rem);
}

.demo-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: clamp(0.75rem, 2vw, 1.25rem);
  display: flex;
  flex-direction: column;
  gap: clamp(0.4rem, 1.2vw, 0.625rem);
}

.field-detected .form-input {
  @apply border-blue-400;
  animation: pulse-glow 0.8s ease-out;
  box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
}

.form-input {
  font-size: clamp(0.8rem, 1.8vw, 1rem);
  padding: clamp(0.5rem, 1.5vw, 0.625rem) clamp(0.75rem, 2vw, 0.875rem);
  border-radius: clamp(4px, 1.2vw, 8px);
  transition: all 0.3s ease;
  pointer-events: none;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-input {
    padding: 0.5rem 0.75rem;
  }
}

@media (max-width: 640px) {
  .form-input {
    padding: 0.4rem 0.6rem;
    font-size: 0.85rem;
  }
}
</style>
