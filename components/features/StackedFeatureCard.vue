<template>
  <div class="stacked-card" ref="cardRef" :data-index="index">
    <div class="card-content">
      <div class="grid lg:grid-cols-12 gap-8 md:gap-12 items-center">
        <!-- Content Section -->
        <div class="lg:col-span-5 space-y-8 order-2" :class="{ 'lg:order-1': !reverse, 'lg:order-2': reverse }">
          <div class="icon-wrapper">
            <component :is="icon" class="icon" />
          </div>
          <div class="space-y-4">
            <h3 class="feature-title">
              {{ title }}
            </h3>
            <p class="feature-description">
              {{ description }}
            </p>
          </div>
        </div>

        <!-- Demo/Image Section -->
        <div class="lg:col-span-7 order-1" :class="{ 'lg:order-2': !reverse, 'lg:order-1': reverse }">
          <div class="demo-wrapper group">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <component
              v-if="demoComponent"
              :is="demoComponent"
              class="w-full h-full relative z-10"
            />
            <img
              v-else
              :src="image"
              :alt="t(`features.${translationKey}.alt`)"
              class="w-full h-full object-contain relative z-10 transform transition-transform duration-700 group-hover:scale-105"
              loading="lazy"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Component } from 'vue'
import { ref, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'

// Note: GSAP is imported only when needed in the onMounted lifecycle hook

const { t } = useI18n()

interface Props {
  icon: Component
  title: string
  description: string
  image?: string
  demoComponent?: Component
  reverse?: boolean
  translationKey: string
  index: number
  totalCards: number
}

const props = withDefaults(defineProps<Props>(), {
  reverse: false,
  image: '',
  demoComponent: undefined
})

const cardRef = ref<HTMLElement | null>(null)
let scrollTriggerInstance: any = null

onMounted(async () => {
  // Check if we're in the browser environment
  if (typeof window === 'undefined') return

  // Dynamically import GSAP to ensure it only runs on the client side
  const { gsap } = await import('gsap')
  const { ScrollTrigger } = await import('gsap/ScrollTrigger')

  // Register the ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger)

  if (!cardRef.value) return

  // Only apply GSAP animation on desktop (lg breakpoint and above)
  const isDesktop = window.matchMedia('(min-width: 1024px)').matches
  if (!isDesktop) return

  const card = cardRef.value
  const isLastCard = props.index === props.totalCards - 1

  // Initialize card with full visibility
  gsap.set(card, {
    opacity: 1,
    scale: 1,
    rotation: 0
  })

  // Calculate dynamic top position to center the card
  // Header is approx 88px (top-6 + h-16) + some buffer
  const headerHeight = 100
  const viewportHeight = window.innerHeight
  const cardHeight = card.offsetHeight
  
  // Calculate the top position to center the card in the available space below header
  // Available space = viewportHeight - headerHeight
  // Center = headerHeight + (Available space - cardHeight) / 2
  let topPosition = headerHeight + (viewportHeight - headerHeight - cardHeight) / 2
  
  // Ensure it doesn't go too high (overlap header) or too low
  topPosition = Math.max(headerHeight + 20, topPosition)

  // Apply sticky positioning only if it's not the last card
  if (!isLastCard) {
    card.style.position = 'sticky'
    card.style.top = `${topPosition}px`
  } else {
    card.style.position = 'relative'
    // card.style.marginTop = '50vh' // Removed extra spacing as previous card already has margin-bottom
  }
  card.style.zIndex = `${10 + props.index}` // Ensure proper stacking order

  // For all cards except the last one, create animation
  if (!isLastCard) {
    // Determine rotation direction: even index = left, odd index = right
    const rotationDirection = props.index % 2 === 0 ? -1 : 1
    const maxRotation = 4

    scrollTriggerInstance = ScrollTrigger.create({
      trigger: card,
      start: `top ${topPosition}px`, // Start animating when it sticks
      end: `+=${viewportHeight * 0.8}`, // Animate over 80% of viewport height
      scrub: true,
      onUpdate: (self) => {
        const progress = self.progress

        let opacity, scale, rotation

        if (progress < 0.3) { // Stay fully visible for first 30%
          opacity = 1
          scale = 1
          rotation = 0
        } else {
          const fadeProgress = (progress - 0.3) / 0.7
          opacity = 1 - fadeProgress
          scale = 1 - fadeProgress * 0.1
          rotation = fadeProgress * maxRotation * rotationDirection
        }

        gsap.set(card, {
          opacity,
          scale,
          rotation
        })
      }
    })
  }
})

onUnmounted(() => {
  if (scrollTriggerInstance) {
    scrollTriggerInstance.kill()
  }
})
</script>

<style scoped>
.stacked-card {
  transform-origin: top center;
  will-change: transform, opacity;
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

/* Mobile: add spacing between cards */
@media (max-width: 1023px) {
  .stacked-card {
    margin-bottom: 3rem;
  }
}

/* Desktop: Reduce spacing to ensure proper stacking overlap */
@media (min-width: 1024px) {
  .stacked-card {
    margin-bottom: 50vh; /* Increased to 50vh to allow independent display space */
  }

  /* Remove spacing for the last card */
  .stacked-card:last-child {
    margin-bottom: 0;
  }
}

.card-content {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  border-radius: 40px; /* More rounded */
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow:
    0 20px 40px -10px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(0, 0, 0, 0.02),
    inset 0 0 0 1px rgba(255, 255, 255, 0.8);
  padding: 2.5rem;
  transition: all 0.6s cubic-bezier(0.22, 1, 0.36, 1);
  min-height: 600px; /* Ensure consistent height for all cards */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.card-content:hover {
  background: rgba(255, 255, 255, 0.98);
  box-shadow:
    0 40px 80px -20px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(0, 0, 0, 0.03),
    inset 0 0 0 1px rgba(255, 255, 255, 1);
  transform: translateY(-2px);
}

@media (min-width: 768px) {
  .card-content {
    padding: 3.5rem;
  }
}

@media (min-width: 1024px) {
  .card-content {
    padding: 4rem;
  }
}

/* Demo wrapper */
.demo-wrapper {
  position: relative;
  width: 100%;
  aspect-ratio: 16/10; /* Fixed aspect ratio */
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24px;
  overflow: hidden;
  background: #f8fafc;
  border: 1px solid rgba(229, 231, 235, 0.4);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
}

/* Icon wrapper */
.icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: white;
  border: 1px solid rgba(229, 231, 235, 0.5);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.02),
    0 1px 2px rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;
}

.icon-wrapper:hover {
  transform: scale(1.05);
  box-shadow: 
    0 8px 24px rgba(0, 0, 0, 0.04),
    0 2px 4px rgba(0, 0, 0, 0.02);
}

.icon {
  width: 28px;
  height: 28px;
  color: #111827; /* Darker icon color */
}

/* Typography */
.feature-title {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  color: #111827;
  letter-spacing: -0.02em;
}

@media (min-width: 768px) {
  .feature-title {
    font-size: 2.5rem;
  }
}

.feature-description {
  font-size: 1.125rem;
  line-height: 1.7;
  color: #4b5563;
}

@media (min-width: 768px) {
  .feature-description {
    font-size: 1.25rem;
  }
}
</style>
