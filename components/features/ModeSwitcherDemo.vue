<template>
  <div class="demo-container">
    <div class="demo-content">
      <div class="fillify-demo-container" style="position: relative;">
        <!-- Tab selector -->
        <div class="fillify-tab-selector" :class="{ 'custom-active': currentTab === 'custom' }">
          <button
            type="button"
            class="fillify-tab-btn"
            :class="{ 'active': currentTab === 'smart' }"
            @click="switchTab('smart')"
          >
            Smart
          </button>
          <button
            type="button"
            class="fillify-tab-btn"
            :class="{ 'active': currentTab === 'custom' }"
            @click="switchTab('custom')"
          >
            {{ currentTab === 'custom' ? modes[currentModeIndex].label : 'Custom' }}
          </button>
        </div>

        <!-- Description textarea -->
        <div class="fillify-textarea-wrapper">
          <textarea
            :value="displayedPrompt"
            readonly
            class="fillify-textarea"
            :placeholder="getPlaceholder()"
          ></textarea>
        </div>

        <!-- Generate button -->
        <button
          class="fillify-generate-btn"
          :class="{ 'generating': isGenerating, 'success': isSuccess }"
        >
          <span class="fillify-button-text">
            {{ isSuccess ? 'Finish' : (isGenerating ? 'Generating...' : 'Generate') }}
          </span>
          <svg v-if="!isGenerating && !isSuccess" class="fillify-sparkle-icon" viewBox="0 0 24 24" fill="currentColor">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4.5a.75.75 0 01.721.544l.813 2.846a3.75 3.75 0 002.576 2.576l2.846.813a.75.75 0 010 1.442l-2.846.813a3.75 3.75 0 00-2.576 2.576l-.813 2.846a.75.75 0 01-1.442 0l-.813-2.846a3.75 3.75 0 00-2.576-2.576l-2.846-.813a.75.75 0 010-1.442l2.846-.813a3.75 3.75 0 002.576-2.576l.813-2.846A.75.75 0 019 4.5zM18 1.5a.75.75 0 01.728.568l.258 1.036c.236.94.97 1.674 1.91 1.91l1.036.258a.75.75 0 010 1.456l-1.036.258c-.94.236-1.674.97-1.91 1.91l-.258 1.036a.75.75 0 01-1.456 0l-.258-1.036a2.625 2.625 0 00-1.91-1.91l-1.036-.258a.75.75 0 010-1.456l1.036-.258a2.625 2.625 0 001.91-1.91l.258-1.036A.75.75 0 0118 1.5zM16.5 15a.75.75 0 01.712.513l.394 1.183c.15.447.5.799.948.948l1.183.395a.75.75 0 010 1.422l-1.183.395c-.447.15-.799.5-.948.948l-.395 1.183a.75.75 0 01-1.422 0l-.395-1.183a1.5 1.5 0 00-.948-.948l-1.183-.395a.75.75 0 010-1.422l1.183-.395c.447-.15.799-.5.948-.948l.395-1.183A.75.75 0 0116.5 15z"></path>
          </svg>
          <div v-if="isGenerating" class="fillify-loading-animation"></div>
        </button>

        <!-- Confetti Canvas -->
        <canvas ref="confettiCanvas" class="fillify-confetti-canvas"></canvas>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'

interface Mode {
  id: 'general' | 'email' | 'bugReport'
  label: string
  prompt: string
  placeholder: string
}

interface TabType {
  id: 'smart' | 'custom'
}

const modes: Mode[] = [
  {
    id: 'general',
    label: 'General',
    prompt: 'Fill out this job application form with my resume information including work experience at Tech Corp.',
    placeholder: 'Enter description'
  },
  {
    id: 'email',
    label: 'Email',
    prompt: 'Write a professional follow-up email to the client about the project timeline and deliverables.',
    placeholder: 'Enter email content description'
  },
  {
    id: 'bugReport',
    label: 'Bug Report',
    prompt: 'Create a bug report for login page error: users cannot login after password reset.',
    placeholder: 'Enter bug description'
  }
]

// Define Smart prompt
const smartPrompt = 'Just describe what you need, and AI will intelligently fill any form, compose emails, or create detailed bug reports automatically.'

const currentTab = ref<'smart' | 'custom'>('smart')
const currentModeIndex = ref(0) // Use index to cycle through modes
const displayedPrompt = ref('')
const isTyping = ref(false)
const isGenerating = ref(false)
const isSuccess = ref(false)
const confettiCanvas = ref<HTMLCanvasElement | null>(null)
let typingTimeout: NodeJS.Timeout | null = null
let animationFrame: number | null = null
let tabSwitchInterval: NodeJS.Timeout | null = null
let customModesShown = 0; // Counter to track how many modes in Custom tab have been shown in current cycle

const getPlaceholder = () => {
  if (currentTab.value === 'smart') {
    return 'Experience AI-powered smart automation'
  }
  return modes[currentModeIndex.value]?.placeholder || 'Enter description'
}

const switchTab = (tab: 'smart' | 'custom') => {
  currentTab.value = tab
  if (typingTimeout) clearTimeout(typingTimeout)
  if (tabSwitchInterval) clearInterval(tabSwitchInterval)
  
  // Reset to initial state for the new tab
  if (tab === 'smart') {
    typeWriter(smartPrompt)
  } else {
    // Reset to first mode when switching to custom
    currentModeIndex.value = 0
    typeWriter(modes[0].prompt) // This will trigger cycling after completion
  }
}

// No longer using interval-based cycling, instead trigger next mode after animation completes
const startModeCycling = () => {
  if (tabSwitchInterval) clearInterval(tabSwitchInterval)
}

const typeWriter = async (text: string) => {
  displayedPrompt.value = ''
  isTyping.value = true
  isSuccess.value = false

  for (let i = 0; i < text.length; i++) {
    await new Promise(resolve => {
      typingTimeout = setTimeout(resolve, 30 + Math.random() * 30)
    })
    displayedPrompt.value += text[i]
  }

  isTyping.value = false

  // Simulate generating and success for both tabs (to allow natural flow)
  // Simulate generating
  await new Promise(resolve => setTimeout(resolve, 500))
  isGenerating.value = true
  await new Promise(resolve => setTimeout(resolve, 2000))
  isGenerating.value = false

  // Show success state
  isSuccess.value = true
  createConfetti()

  // Wait 3 seconds before continuing
  await new Promise(resolve => setTimeout(resolve, 3000))
  isSuccess.value = false
  
  // After completion, automatically switch to next stage
  if (currentTab.value === 'smart') {
    // After Smart tab completes, switch to Custom tab and reset counter
    customModesShown = 0;
    setTimeout(() => {
      switchTab('custom') // This will start the cycling in Custom tab
    }, 100) // Small delay to ensure transition
  } else if (currentTab.value === 'custom') {
    // For Custom tab, we need to track if we've shown all 3 modes in this cycle
    customModesShown++;
    
    // If we've shown all 3 modes (General, Email, Bug Report) in this cycle, go back to Smart
    if (customModesShown >= 3) {
      customModesShown = 0; // Reset counter
      setTimeout(() => {
        switchTab('smart') // Go back to Smart tab to restart the cycle
      }, 100) // Small delay to ensure transition
    } else {
      // Otherwise, move to next mode in Custom tab
      setTimeout(() => {
        // Move to next mode
        currentModeIndex.value = (currentModeIndex.value + 1) % modes.length
        if (typingTimeout) clearTimeout(typingTimeout)
        typeWriter(modes[currentModeIndex.value].prompt)
      }, 100) // Small delay to ensure transition
    }
  }
}

const createConfetti = () => {
  const canvas = confettiCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // Set canvas size to match container
  const container = canvas.parentElement
  if (container) {
    canvas.width = container.offsetWidth
    canvas.height = container.offsetHeight
  }

  // Confetti configuration
  const confettiConfig = {
    confettiCount: 20,
    colors: [
      { front: '#7b5cff', back: '#6245e0' },
      { front: '#b3c7ff', back: '#8fa5e5' },
      { front: '#5c86ff', back: '#345dd1' }
    ]
  }

  interface Particle {
    x: number
    y: number
    vx: number
    vy: number
    color: string
    size: number
  }

  const confetti: Particle[] = []

  // Create confetti particles
  for (let i = 0; i < confettiConfig.confettiCount; i++) {
    confetti.push({
      x: canvas.width / 2,
      y: canvas.height / 2,
      vx: (Math.random() - 0.5) * 10,
      vy: Math.random() * -10 - 5,
      color: confettiConfig.colors[Math.floor(Math.random() * confettiConfig.colors.length)].front,
      size: Math.random() * 6 + 4
    })
  }

  // Animate confetti
  const animate = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    confetti.forEach((particle, index) => {
      particle.x += particle.vx
      particle.y += particle.vy
      particle.vy += 0.3 // gravity

      ctx.fillStyle = particle.color
      ctx.fillRect(particle.x, particle.y, particle.size, particle.size)

      if (particle.y > canvas.height + 100) {
        confetti.splice(index, 1)
      }
    })

    if (confetti.length > 0) {
      animationFrame = requestAnimationFrame(animate)
    }
  }

  animate()
}

watch(currentTab, () => {
  if (typingTimeout) clearTimeout(typingTimeout)
  if (tabSwitchInterval) clearInterval(tabSwitchInterval)
  
  if (currentTab.value === 'smart') {
    typeWriter(smartPrompt)
  } else {
    // Reset to first mode when switching to custom
    currentModeIndex.value = 0
    typeWriter(modes[0].prompt)
    startModeCycling()
  }
})

onMounted(() => {
  // Initialize based on current tab
  if (currentTab.value === 'smart') {
    typeWriter(smartPrompt)
  } else {
    // For Custom tab, start with first mode
    currentModeIndex.value = 0
    typeWriter(modes[0].prompt)
  }
})

onUnmounted(() => {
  if (typingTimeout) clearTimeout(typingTimeout)
  if (animationFrame) cancelAnimationFrame(animationFrame)
  if (tabSwitchInterval) clearInterval(tabSwitchInterval)
})
</script>

<style scoped>
.demo-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FCE7F3 0%, #FBE8FF 100%);
  border-radius: 20px;
  padding: clamp(1rem, 2.5vw, 2rem);
  pointer-events: none; /* Disable all mouse interactions */
  user-select: none; /* Disable text selection */
}

.demo-content {
  width: 100%;
  max-width: 28rem;
  font-size: clamp(0.8rem, 2vw, 1rem);
}

.fillify-demo-container {
  background: white;
  border-radius: 12px;
  padding: clamp(12px, 2.5vw, 20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  gap: clamp(8px, 2vw, 12px);
}



/* Tab selector */
.fillify-tab-selector {
  position: relative;
  display: flex;
  gap: clamp(2px, 1vw, 4px);
  padding: clamp(2px, 1vw, 4px);
  background: #f5f5f5;
  border-radius: clamp(4px, 1.5vw, 8px);
}

/* Dynamic background for tabs - only Smart vs Custom/Mode tabs */
.fillify-tab-selector::after {
  content: '';
  position: absolute;
  top: clamp(2px, 1vw, 4px);
  left: clamp(2px, 1vw, 4px);
  width: calc((100% - (2 * clamp(2px, 1vw, 4px) + clamp(2px, 1vw, 4px))) / 2);
  height: calc(100% - 8px);
  background: white;
  border-radius: clamp(3px, 1vw, 6px);
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 0;
}

/* When Custom tab is active (including its sub modes), move background to second position */
.fillify-tab-selector.custom-active::after {
  left: calc(clamp(2px, 1vw, 4px) + (100% - (2 * clamp(2px, 1vw, 4px) + clamp(2px, 1vw, 4px))) / 2 + clamp(2px, 1vw, 4px));
}

.fillify-tab-btn {
  flex: 1;
  padding: clamp(5px, 1.5vw, 8px) clamp(8px, 2vw, 12px);
  border: none;
  background: transparent;
  color: #666;
  font-size: clamp(12px, 2vw, 14px);
  cursor: default; /* Change from pointer to default */
  border-radius: clamp(3px, 1vw, 6px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
  user-select: none;
  font-weight: 500;
  min-width: 0; /* Allow flex item to shrink below content size */
  pointer-events: none; /* Disable click events */
}

.fillify-tab-btn.active {
  color: #2962FF;
}

/* Textarea wrapper */
.fillify-textarea-wrapper {
  position: relative;
  width: 100%;
}

.fillify-textarea {
  width: 100%;
  padding: clamp(10px, 2vw, 14px);
  border: 1px solid #e0e0e0;
  border-radius: clamp(4px, 1.5vw, 8px);
  font-size: clamp(12px, 2vw, 14px);
  line-height: 1.5;
  resize: none;
  height: clamp(80px, 15vw, 120px);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #fff;
  color: #333;
  transition: all 0.3s ease;
}

.fillify-textarea:focus {
  outline: none;
  border-color: #2962FF;
  box-shadow: 0 0 0 3px rgba(41, 98, 255, 0.1);
}

/* Generate button */
.fillify-generate-btn {
  width: 100%;
  padding: clamp(8px, 2vw, 12px);
  border: none;
  border-radius: clamp(4px, 1.5vw, 8px);
  background: #2962FF;
  color: white;
  font-size: clamp(14px, 2.2vw, 16px);
  font-weight: 500;
  cursor: default; /* Change from pointer to default */
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: clamp(36px, 8vw, 48px);
  gap: clamp(4px, 1.5vw, 6px);
  pointer-events: none; /* Disable click events */
}

.fillify-generate-btn:hover:not(.generating) {
  background: #2962FF; /* Keep same color, no hover effect */
}

.fillify-generate-btn.generating {
  background: #1E4EE3;
  cursor: not-allowed;
}

.fillify-generate-btn.success {
  background: #2962FF;
  cursor: default;
  color: white;
}

/* Sparkle icon animation */
.fillify-sparkle-icon {
  width: clamp(12px, 2.5vw, 16px);
  height: clamp(12px, 2.5vw, 16px);
  color: inherit;
  opacity: 0.9;
  animation: sparkle 2s ease-in-out infinite;
  transform-box: fill-box;
  transform-origin: center;
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

.fillify-generate-btn:hover .fillify-sparkle-icon {
  animation: sparkle-hover 1s ease-in-out infinite;
}

@keyframes sparkle-hover {
  0%, 100% {
    transform: scale(1.1) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.2) rotate(10deg);
    opacity: 0.9;
  }
}

/* Loading animation */
.fillify-loading-animation {
  position: absolute;
  border-radius: 100%;
  animation: ripple 0.6s linear infinite;
}

@keyframes ripple {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.1),
               0 0 0 40px rgba(255, 255, 255, 0.1),
               0 0 0 80px rgba(255, 255, 255, 0.1),
               0 0 0 120px rgba(255, 255, 255, 0.1);
  }
  100% {
    box-shadow: 0 0 0 40px rgba(255, 255, 255, 0.1),
               0 0 0 80px rgba(255, 255, 255, 0.1),
               0 0 0 120px rgba(255, 255, 255, 0.1),
               0 0 0 160px rgba(255, 255, 255, 0);
  }
}

/* Confetti canvas */
.fillify-confetti-canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1000;
  width: 100%;
  height: 100%;
}
</style>
