<template>
  <div class="relative pointer-events-none">
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      class="drop-shadow-lg"
    >
      <path
        d="M7.5 5L26.5 18.5L16.5 20.5L13.5 29.5L7.5 5Z"
        fill="#111827"
        stroke="white"
        stroke-width="2"
        stroke-linejoin="round"
      />
    </svg>
    <div
      v-if="label"
      class="absolute top-6 left-4 bg-gray-900 text-white text-xs font-bold px-2 py-1 rounded-md whitespace-nowrap shadow-lg"
    >
      {{ label }}
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  label?: string
}>()
</script>
