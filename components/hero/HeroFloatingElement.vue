<template>
  <div 
    class="hero-floating-element group"
    :class="[
      type === 'login' ? 'w-48' : '',
      type === 'bug' ? 'w-56' : '',
      type === 'email' ? 'w-52' : '',
      type === 'profile' ? 'w-44' : ''
    ]"
  >
    <!-- Header -->
    <div class="flex items-center gap-2 mb-3 border-b border-gray-100 pb-2">
      <div class="p-1.5 rounded-lg" :class="bgClass">
        <component :is="icon" class="w-3.5 h-3.5" :class="textClass" />
      </div>
      <div class="h-2 w-16 bg-gray-200 rounded-full"></div>
    </div>

    <!-- Body Content -->
    <div class="space-y-2">
      <!-- Login Form Simulation -->
      <template v-if="type === 'login'">
        <div class="space-y-1.5">
          <div class="h-2 w-8 bg-gray-200 rounded-full"></div>
          <div class="h-6 w-full bg-gray-50 border border-gray-100 rounded-md group-hover:border-blue-200 transition-colors"></div>
        </div>
        <div class="space-y-1.5">
          <div class="h-2 w-10 bg-gray-200 rounded-full"></div>
          <div class="h-6 w-full bg-gray-50 border border-gray-100 rounded-md group-hover:border-blue-200 transition-colors"></div>
        </div>
        <div class="pt-1">
          <div class="h-6 w-full bg-blue-500 rounded-md opacity-90 group-hover:opacity-100 transition-opacity"></div>
        </div>
      </template>

      <!-- Bug Report Simulation -->
      <template v-if="type === 'bug'">
        <div class="flex gap-2 mb-2">
          <div class="h-2 w-12 bg-red-100 rounded-full"></div>
          <div class="h-2 w-8 bg-gray-200 rounded-full"></div>
        </div>
        <div class="h-16 w-full bg-gray-50 border border-gray-100 rounded-md p-2 group-hover:border-red-200 transition-colors">
          <div class="space-y-1.5">
            <div class="h-1.5 w-full bg-gray-200 rounded-full"></div>
            <div class="h-1.5 w-3/4 bg-gray-200 rounded-full"></div>
            <div class="h-1.5 w-1/2 bg-gray-200 rounded-full"></div>
          </div>
        </div>
        <div class="flex justify-end pt-1">
          <div class="h-5 w-16 bg-red-500 rounded-md opacity-90 group-hover:opacity-100 transition-opacity"></div>
        </div>
      </template>

      <!-- Email Simulation -->
      <template v-if="type === 'email'">
        <div class="flex items-center gap-2 border-b border-gray-50 pb-1.5">
          <div class="h-1.5 w-4 bg-gray-300 rounded-full"></div>
          <div class="h-1.5 w-24 bg-gray-100 rounded-full"></div>
        </div>
        <div class="flex items-center gap-2 border-b border-gray-50 pb-1.5">
          <div class="h-1.5 w-8 bg-gray-300 rounded-full"></div>
          <div class="h-1.5 w-20 bg-gray-100 rounded-full"></div>
        </div>
        <div class="pt-1 space-y-1.5">
          <div class="h-1.5 w-full bg-gray-100 rounded-full"></div>
          <div class="h-1.5 w-full bg-gray-100 rounded-full"></div>
          <div class="h-1.5 w-2/3 bg-gray-100 rounded-full"></div>
        </div>
      </template>

      <!-- Profile/Auto-fill Simulation -->
      <template v-if="type === 'profile'">
        <div class="flex items-center gap-3 mb-3">
          <div class="w-8 h-8 rounded-full bg-gray-100 border border-gray-200"></div>
          <div class="space-y-1.5 flex-1">
            <div class="h-2 w-20 bg-gray-200 rounded-full"></div>
            <div class="h-1.5 w-12 bg-gray-100 rounded-full"></div>
          </div>
        </div>
        <div class="space-y-2">
          <div class="h-5 w-full bg-gray-50 border border-gray-100 rounded-md flex items-center px-2">
             <div class="h-1.5 w-16 bg-gray-200 rounded-full"></div>
          </div>
          <div class="h-5 w-full bg-gray-50 border border-gray-100 rounded-md flex items-center px-2">
             <div class="h-1.5 w-24 bg-gray-200 rounded-full"></div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Component } from 'vue'
import { computed } from 'vue'

interface Props {
  type: 'login' | 'bug' | 'email' | 'profile'
  icon: Component
  color?: string
}

const props = defineProps<Props>()

const bgClass = computed(() => {
  switch (props.type) {
    case 'login': return 'bg-blue-50'
    case 'bug': return 'bg-red-50'
    case 'email': return 'bg-purple-50'
    case 'profile': return 'bg-green-50'
    default: return 'bg-gray-50'
  }
})

const textClass = computed(() => {
  switch (props.type) {
    case 'login': return 'text-blue-600'
    case 'bug': return 'text-red-600'
    case 'email': return 'text-purple-600'
    case 'profile': return 'text-green-600'
    default: return 'text-gray-600'
  }
})
</script>

<style scoped>
.hero-floating-element {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.6);
  box-shadow: 
    0 10px 30px -5px rgba(0, 0, 0, 0.05),
    0 4px 12px -2px rgba(0, 0, 0, 0.02);
  border-radius: 16px;
  padding: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hero-floating-element:hover {
  transform: translateY(-4px) scale(1.02);
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 
    0 20px 40px -5px rgba(0, 0, 0, 0.1),
    0 8px 16px -4px rgba(0, 0, 0, 0.04);
  border-color: rgba(255, 255, 255, 1);
}
</style>
