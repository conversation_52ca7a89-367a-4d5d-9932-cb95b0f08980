<template>
  <div class="full-chrome-demo-container">
    <!-- Chrome Browser Frame -->
    <div class="chrome-browser">
      <!-- Top Bar (macOS style) -->
      <div class="chrome-topbar">
        <div class="chrome-dots">
          <div class="chrome-dot red"></div>
          <div class="chrome-dot yellow"></div>
          <div class="chrome-dot green"></div>
        </div>

        <!-- Tab Bar (Inside topbar) -->
        <div class="chrome-tabbar">
          <div class="chrome-tab">
            <div class="chrome-tab-fade"></div>
            <div class="chrome-tab-close">
              <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0.75 0.75L3.75 3.75M3.75 3.75L6.75 0.75M3.75 3.75L0.75 6.75M3.75 3.75L6.75 6.75" stroke="#3D4043" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="chrome-tab-favicon">
              <svg width="14" height="14" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M811.311 65H122C94.3858 65 72 87.3858 72 115V908C72 935.614 94.3858 958 122 958H902.481C930.096 958 952.481 935.614 952.481 908V206.17L811.311 65Z" fill="url(#fillify-favicon-gradient)"/>
                <path d="M953 207H811V65L882 136L953 207Z" fill="#D9D9D9" opacity="0.5"/>
                <rect x="337.211" y="334.847" width="504.457" height="81.6033" fill="white"/>
                <rect x="337.211" y="498.054" width="504.457" height="81.6033" fill="white"/>
                <rect x="337.211" y="661.261" width="504.457" height="81.6033" fill="white"/>
                <path d="M242.625 323.72L257.152 362.977L296.409 377.504L257.152 392.03L242.625 431.288L228.099 392.03L188.841 377.504L228.099 362.977L242.625 323.72Z" fill="white"/>
                <path d="M242.625 486.926L257.152 526.184L296.409 540.71L257.152 555.237L242.625 594.494L228.099 555.237L188.841 540.71L228.099 526.184L242.625 486.926Z" fill="white"/>
                <path d="M242.625 650.133L257.152 689.39L296.409 703.917L257.152 718.444L242.625 757.701L228.099 718.444L188.841 703.917L228.099 689.39L242.625 650.133Z" fill="white"/>
                <defs>
                  <linearGradient id="fillify-favicon-gradient" x1="952" y1="65" x2="71.9999" y2="958" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#1C5BEE"/>
                    <stop offset="1" stop-color="#5C82DB"/>
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <div class="chrome-tab-title-wrapper">
              <div class="chrome-tab-title">Post Your Listing - Classifieds</div>
            </div>
          </div>
        </div>

        <!-- New Tab Button -->
        <div class="chrome-new-tab-btn">
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.75 0.75V5.75M5.75 10.75V5.75M5.75 5.75H0.75M5.75 5.75H10.75" stroke="#3D4043" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>

        <!-- Window Controls -->
        <div class="chrome-window-controls">
          <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0.75 0.75L4.75 4.75L8.75 0.75" stroke="#3D4043" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>

      <!-- Address Bar -->
      <div class="chrome-addressbar">
        <!-- Back Button -->
        <div class="chrome-back-btn">
          <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12.75 6.75H0.75M0.75 6.75L6.75 0.75M0.75 6.75L6.75 12.75" stroke="#606367" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>

        <!-- Forward Button -->
        <div class="chrome-forward-btn">
          <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0.75 6.75H12.75M12.75 6.75L6.75 0.75M12.75 6.75L6.75 12.75" stroke="#606367" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>

        <!-- Refresh Button -->
        <div class="chrome-refresh-btn">
          <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13 7C13 10.3137 10.3137 13 7 13C3.68629 13 1 10.3137 1 7C1 3.68629 3.68629 1 7 1C9.09894 1 10.9543 2.08885 12 3.75M12 1V3.75M12 3.75H9.25" stroke="#606367" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>

        <!-- URL Bar -->
        <div class="chrome-url-bar">
          <div class="chrome-lock-icon">
            <svg width="8" height="11" viewBox="0 0 8 11" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M4 0C2.61929 0 1.5 1.11929 1.5 2.5V3.5H1C0.447715 3.5 0 3.94772 0 4.5V9.5C0 10.0523 0.447715 10.5 1 10.5H7C7.55228 10.5 8 10.0523 8 9.5V4.5C8 3.94772 7.55228 3.5 7 3.5H6.5V2.5C6.5 1.11929 5.38071 0 4 0ZM5.5 3.5V2.5C5.5 1.67157 4.82843 1 4 1C3.17157 1 2.5 1.67157 2.5 2.5V3.5H5.5Z" fill="#606367"/>
            </svg>
          </div>
          <div class="chrome-url-text-wrapper">
            <div class="chrome-url">classifieds.example.com/post-listing</div>
          </div>
          <div class="chrome-star-icon">
            <svg width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M6.13798 0.449139C6.33256 -0.149712 7.17978 -0.149714 7.37436 0.449139L8.6696 4.43549H12.8611C13.4908 4.43549 13.7526 5.24125 13.2432 5.61135L9.85217 8.07505L11.1474 12.0614C11.342 12.6602 10.6566 13.1582 10.1472 12.7881L6.75617 10.3244L3.36518 12.7881C2.85577 13.1582 2.17035 12.6602 2.36493 12.0614L3.66018 8.07505L0.269185 5.61135C-0.240227 5.24124 0.0215741 4.43549 0.651246 4.43549H4.84274L6.13798 0.449139ZM6.75617 2.59164L5.89685 5.23635C5.80983 5.50417 5.56026 5.68549 5.27867 5.68549H2.49786L4.74758 7.32001C4.9754 7.48553 5.07072 7.77892 4.98371 8.04673L4.12439 10.6914L6.37411 9.05692C6.60193 8.8914 6.91042 8.8914 7.13823 9.05692L9.38795 10.6914L8.52864 8.04673C8.44162 7.77891 8.53695 7.48553 8.76476 7.32001L11.0145 5.68549H8.23368C7.95208 5.68549 7.70251 5.50416 7.61549 5.23635L6.75617 2.59164Z" fill="#626365"/>
            </svg>
          </div>
          <div class="chrome-share-icon">
            <svg width="12" height="15" viewBox="0 0 12 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M6.06694 0.183058C5.82286 -0.0610194 5.42714 -0.0610194 5.18306 0.183058L3.18306 2.18306C2.93898 2.42714 2.93898 2.82286 3.18306 3.06694C3.42714 3.31102 3.82286 3.31102 4.06694 3.06694L5 2.13388V8.625C5 8.97018 5.27982 9.25 5.625 9.25C5.97018 9.25 6.25 8.97018 6.25 8.625V2.13388L7.18306 3.06694C7.42714 3.31102 7.82286 3.31102 8.06694 3.06694C8.31102 2.82286 8.31102 2.42714 8.06694 2.18306L6.06694 0.183058ZM1.25 6.125C1.25 5.64175 1.64175 5.25 2.125 5.25H3.125C3.47018 5.25 3.75 4.97018 3.75 4.625C3.75 4.27982 3.47018 4 3.125 4H2.125C0.951395 4 0 4.9514 0 6.125V12.125C0 13.2986 0.951395 14.25 2.125 14.25H9.125C10.2986 14.25 11.25 13.2986 11.25 12.125V6.125C11.25 4.9514 10.2986 4 9.125 4H8.125C7.77982 4 7.5 4.27982 7.5 4.625C7.5 4.97018 7.77982 5.25 8.125 5.25H9.125C9.60825 5.25 10 5.64175 10 6.125V12.125C10 12.6082 9.60825 13 9.125 13H2.125C1.64175 13 1.25 12.6082 1.25 12.125V6.125Z" fill="#626365"/>
            </svg>
          </div>
        </div>

        <!-- Extension Icon -->
        <div class="chrome-extension-icon" ref="extensionIcon" @click="handleExtensionClick" title="Fillify">
          <svg width="18" height="18" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M811.311 65H122C94.3858 65 72 87.3858 72 115V908C72 935.614 94.3858 958 122 958H902.481C930.096 958 952.481 935.614 952.481 908V206.17L811.311 65Z" fill="url(#fillify-extension-gradient)"/>
            <path d="M953 207H811V65L882 136L953 207Z" fill="#D9D9D9" opacity="0.5"/>
            <rect x="337.211" y="334.847" width="504.457" height="81.6033" fill="white"/>
            <rect x="337.211" y="498.054" width="504.457" height="81.6033" fill="white"/>
            <rect x="337.211" y="661.261" width="504.457" height="81.6033" fill="white"/>
            <path d="M242.625 323.72L257.152 362.977L296.409 377.504L257.152 392.03L242.625 431.288L228.099 392.03L188.841 377.504L228.099 362.977L242.625 323.72Z" fill="white"/>
            <path d="M242.625 486.926L257.152 526.184L296.409 540.71L257.152 555.237L242.625 594.494L228.099 555.237L188.841 540.71L228.099 526.184L242.625 486.926Z" fill="white"/>
            <path d="M242.625 650.133L257.152 689.39L296.409 703.917L257.152 718.444L242.625 757.701L228.099 718.444L188.841 703.917L228.099 689.39L242.625 650.133Z" fill="white"/>
            <defs>
              <linearGradient id="fillify-extension-gradient" x1="952" y1="65" x2="71.9999" y2="958" gradientUnits="userSpaceOnUse">
                <stop stop-color="#1C5BEE"/>
                <stop offset="1" stop-color="#5C82DB"/>
              </linearGradient>
            </defs>
          </svg>
          <div class="click-ripple" ref="clickRipple"></div>
        </div>

        <!-- Profile Icon -->
        <div class="chrome-profile-icon">
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="9" cy="9" r="9" fill="#D9D9D9"/>
            <circle cx="3.41947" cy="3.41947" r="3.41947" transform="matrix(1 0 0 -1 5.58057 9.72986)" fill="#757575"/>
            <path d="M2.55347 14.3225C2.55347 12.1136 4.34415 10.3229 6.55306 10.3229H11.4468C13.6557 10.3229 15.4464 12.1136 15.4464 14.3225H2.55347Z" fill="#757575"/>
          </svg>
        </div>

        <!-- Menu Icon -->
        <div class="chrome-menu-icon">
          <svg width="3" height="14" viewBox="0 0 3 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M3 1.5C3 2.32843 2.32843 3 1.5 3C0.671573 3 0 2.32843 0 1.5C0 0.671573 0.671573 0 1.5 0C2.32843 0 3 0.671573 3 1.5ZM3 6.75C3 7.57843 2.32843 8.25 1.5 8.25C0.671573 8.25 0 7.57843 0 6.75C0 5.92157 0.671573 5.25 1.5 5.25C2.32843 5.25 3 5.92157 3 6.75ZM1.5 13.5C2.32843 13.5 3 12.8284 3 12C3 11.1716 2.32843 10.5 1.5 10.5C0.671573 10.5 0 11.1716 0 12C0 12.8284 0.671573 13.5 1.5 13.5Z" fill="#606367"/>
          </svg>
        </div>
      </div>

      <!-- Divider -->
      <div class="chrome-divider"></div>

      <!-- Content Area -->
      <div class="chrome-content">
        <div class="webpage-content">
          <!-- Main Form -->
          <div class="webpage-form">
            <!-- Basic Information Section -->
            <div class="form-section">
              <h2 class="section-title">Post Your Rental Listing</h2>

              <!-- Title (full width) -->
              <div class="form-field full-width">
                <label for="title">Title</label>
                <input type="text" id="title" ref="title" placeholder="Enter a descriptive title">
              </div>

              <!-- Name and Phone -->
              <div class="form-row">
                <div class="form-field">
                  <label for="name">Name</label>
                  <input type="text" id="name" ref="name" placeholder="Your name">
                </div>
                <div class="form-field">
                  <label for="phone">Phone Number</label>
                  <input type="tel" id="phone" ref="phone" placeholder="Your contact number">
                </div>
              </div>
            </div>

            <!-- Detailed Information Section -->
            <div class="form-section">
              <h2 class="section-title">Detailed Information</h2>

              <!-- Category and Price -->
              <div class="form-row">
                <div class="form-field">
                  <label for="category">Category</label>
                  <input type="text" id="category" ref="category" placeholder="Enter category (e.g. Housing, Jobs, Services)">
                </div>
                <div class="form-field">
                  <label for="price">Price</label>
                  <input type="text" id="price" ref="price" placeholder="Enter price">
                </div>
              </div>

              <!-- Address (full width) -->
              <div class="form-field full-width">
                <label for="address">Address</label>
                <input type="text" id="address" ref="address" placeholder="Enter location">
              </div>

              <!-- Description (full width) -->
              <div class="form-field full-width">
                <label for="description">Description</label>
                <textarea id="description" ref="description" rows="5" placeholder="Provide detailed information about your posting"></textarea>
              </div>
            </div>

            <!-- Submit Buttons -->
            <div class="form-actions">
              <button type="button" class="reset-button">Reset</button>
              <button type="submit" class="submit-button">
                <span>Submit</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Fillify Popup -->
      <div id="fillify-popup" ref="popup" class="fillify-popup">
        <div class="fillify-header">
          <h1>Fillify</h1>
          <div class="fillify-header-right">
            <button class="fillify-settings-button">
              <div style="width: 14px; height: 11px; position: relative; display: flex; flex-direction: column; justify-content: space-between;">
                <span style="display: block; width: 100%; height: 1.5px; background-color: #666; border-radius: 2px;"></span>
                <span style="display: block; width: 100%; height: 1.5px; background-color: #666; border-radius: 2px;"></span>
                <span style="display: block; width: 100%; height: 1.5px; background-color: #666; border-radius: 2px;"></span>
              </div>
            </button>
            <button id="fillify-close-btn" @click="hidePopup">
              <svg width="13" height="13" viewBox="0 0 24 24" fill="#666">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        </div>

        <form class="fillify-form-container">
          <div class="fillify-mode-buttons">
            <button type="button" class="fillify-mode-btn active">Smart</button>
            <button type="button" class="fillify-mode-btn">
              <span>Custom</span>
              <svg class="fillify-custom-dropdown-icon" viewBox="0 0 12 8" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="width: 7px; height: 5px; margin-left: 3px; flex-shrink: 0;">
                <polyline points="1,1 6,6 11,1"/>
              </svg>
            </button>
          </div>

          <div style="position: relative;">
            <textarea id="fillify-description" ref="textarea" class="fillify-textarea" placeholder="Describe what you want to fill in..."></textarea>

            <div class="fillify-bottom-toolbar" style="position: absolute; bottom: 6px; left: 11px; right: 11px; display: flex; justify-content: flex-end; align-items: center; height: 21px; pointer-events: none;">
              <div style="display: flex; gap: 9px; align-items: center; pointer-events: auto;">
                <div class="fillify-reference-selector">
                  <div class="fillify-reference-flex" style="position: relative; display: flex; align-items: center; gap: 3px; padding: 0 3px; height: 21px; border-radius: 3px; cursor: pointer;">
                    <svg class="fillify-reference-icon" width="13" height="13" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 13px; height: 13px; flex-shrink: 0; stroke: #4A5056;">
                      <path d="M5 4C5 3.44772 5.44772 3 6 3H18C18.5523 3 19 3.44772 19 4V21L12 17.5L5 21V4Z" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                </div>

                <div class="fillify-language-selector">
                  <div class="fillify-language-flex" style="position: relative; display: flex; align-items: center; gap: 2px; padding: 0 3px; height: 21px; border-radius: 3px; cursor: pointer;">
                    <svg width="13" height="13" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 13px; height: 13px;">
                      <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M3.6001 9H20.4001" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M3.6001 15H20.4001" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M12 21C13.6569 21 15 16.9706 15 12C15 7.02944 13.6569 3 12 3C10.3432 3 9 7.02944 9 12C9 16.9706 10.3432 21 12 21Z" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <div class="fillify-selected-language" style="color: #666; font-size: 9px; margin-right: 2px;">Auto</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <button id="fillify-fill-button" ref="button" type="button" class="fillify-primary-btn" @click="startDemo">
            <div class="fillify-button-content" style="display: flex; align-items: center; gap: 6px; position: relative; z-index: 1;">
              <span class="fillify-button-text" ref="buttonText">Generate</span>
              <svg class="fillify-sparkle-icon" ref="sparkleIcon" viewBox="0 0 24 24" fill="currentColor">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4.5a.75.75 0 01.721.544l.813 2.846a3.75 3.75 0 002.576 2.576l2.846.813a.75.75 0 010 1.442l-2.846.813a3.75 3.75 0 00-2.576 2.576l-.813 2.846a.75.75 0 01-1.442 0l-.813-2.846a3.75 3.75 0 00-2.576-2.576l-2.846-.813a.75.75 0 010-1.442l2.846-.813a3.75 3.75 0 002.576-2.576l.813-2.846A.75.75 0 019 4.5zM18 1.5a.75.75 0 01.728.568l.258 1.036c.236.94.97 1.674 1.91 1.91l1.036.258a.75.75 0 010 1.456l-1.036.258c-.94.236-1.674.97-1.91 1.91l-.258 1.036a.75.75 0 01-1.456 0l-.258-1.036a2.625 2.625 0 00-1.91-1.91l-1.036-.258a.75.75 0 010-1.456l1.036-.258a2.625 2.625 0 001.91-1.91l.258-1.036A.75.75 0 0118 1.5zM16.5 15a.75.75 0 01.712.513l.394 1.183c.15.447.5.799.948.948l1.183.395a.75.75 0 010 1.422l-1.183.395c-.447.15-.799.5-.948.948l-.395 1.183a.75.75 0 01-1.422 0l-.395-1.183a1.5 1.5 0 00-.948-.948l-1.183-.395a.75.75 0 010-1.422l1.183-.395c.447-.15.799-.5.948-.948l.395-1.183A.75.75 0 0116.5 15z"></path>
              </svg>
            </div>
            <div class="fillify-animation"></div>
          </button>

          <canvas class="fillify-confetti-canvas" ref="confettiCanvas"></canvas>
        </form>
      </div>

      <!-- Reasoning Bubble -->
      <div id="reasoning-bubble" ref="reasoningBubble" class="fillify-reasoning-card">
        <div class="fillify-reasoning-blob"></div>
        <div class="fillify-reasoning-bg">
          <button class="fillify-reasoning-close" @click="hideReasoningBubble">×</button>
          <div class="fillify-reasoning-content" ref="reasoningContent">
            Analyzing the form structure and context... Understanding the field relationships... Generating appropriate content based on your description...
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'

// Props to control when animation should start
const props = defineProps<{
  shouldStartAnimation?: boolean
}>()

const popup = ref<HTMLElement | null>(null)
const button = ref<HTMLElement | null>(null)
const buttonText = ref<HTMLElement | null>(null)
const sparkleIcon = ref<HTMLElement | null>(null)
const textarea = ref<HTMLTextAreaElement | null>(null)
const reasoningBubble = ref<HTMLElement | null>(null)
const reasoningContent = ref<HTMLElement | null>(null)
const confettiCanvas = ref<HTMLCanvasElement | null>(null)
const extensionIcon = ref<HTMLElement | null>(null)
const clickRipple = ref<HTMLElement | null>(null)

// Form field refs
const title = ref<HTMLInputElement | null>(null)
const name = ref<HTMLInputElement | null>(null)
const phone = ref<HTMLInputElement | null>(null)
const category = ref<HTMLInputElement | null>(null)
const price = ref<HTMLInputElement | null>(null)
const address = ref<HTMLInputElement | null>(null)
const description = ref<HTMLTextAreaElement | null>(null)

const promptText = "I want to rent out my 2-bedroom apartment in downtown at 123 Main Street, close to subway, $2000/month, available from next month. Contact Michael Chen at (*************."

const formData = {
  'title': 'Spacious 2BR Apartment in Downtown - Close to Metro',
  'name': 'Michael Chen',
  'phone': '(*************',
  'category': 'Housing',
  'price': '$2,000/month',
  'address': '123 Main Street, Downtown, New York, NY 10001',
  'description': 'Beautiful 2-bedroom apartment available for rent in the heart of downtown. Located just 2 blocks from the subway station, this unit features hardwood floors, modern kitchen with stainless steel appliances, in-unit washer/dryer, and plenty of natural light. Building amenities include 24/7 doorman, fitness center, and rooftop terrace. Perfect for professionals or small families. Available from next month. No pets, no smoking.'
}

let animationTimeout: NodeJS.Timeout | null = null

const setState = (state: string) => {
  if (!button.value || !buttonText.value || !sparkleIcon.value) return

  const btn = button.value as HTMLButtonElement

  btn.classList.remove('loading', 'thinking', 'stopping', 'success')
  btn.disabled = false
  sparkleIcon.value.classList.remove('hidden')

  switch (state) {
    case 'generate':
      buttonText.value.textContent = 'Generate'
      break
    case 'generating':
      btn.classList.add('loading')
      btn.disabled = true
      buttonText.value.textContent = 'Generating...'
      sparkleIcon.value.classList.add('hidden')
      break
    case 'thinking':
      btn.classList.add('thinking')
      btn.disabled = true
      buttonText.value.textContent = 'Thinking...'
      sparkleIcon.value.classList.add('hidden')
      break
    case 'stopping':
      btn.classList.add('stopping')
      btn.disabled = true
      buttonText.value.textContent = 'Stopping...'
      sparkleIcon.value.classList.add('hidden')
      break
    case 'finish':
      btn.classList.add('success')
      buttonText.value.textContent = 'Finish'
      sparkleIcon.value.classList.add('hidden')
      createConfetti()
      break
  }
}

const showReasoningBubble = () => {
  if (reasoningBubble.value) {
    reasoningBubble.value.classList.add('show')
  }
}

const hideReasoningBubble = () => {
  if (reasoningBubble.value) {
    reasoningBubble.value.classList.remove('show')
  }
}

const typeText = (element: HTMLTextAreaElement, text: string, speed = 30): Promise<void> => {
  return new Promise((resolve) => {
    let index = 0
    element.value = ''

    const interval = setInterval(() => {
      if (index < text.length) {
        element.value += text[index]
        element.scrollTop = element.scrollHeight
        index++
      } else {
        clearInterval(interval)
        resolve()
      }
    }, speed)
  })
}

const fillField = (fieldRef: any, text: string): void => {
  const field = fieldRef.value
  if (!field) return

  // Just fill the field value, the generating effect is handled globally
  field.value = text
}

const streamReasoningText = (text: string, speed = 30): Promise<void> => {
  return new Promise((resolve) => {
    if (!reasoningContent.value) {
      resolve()
      return
    }

    let index = 0
    reasoningContent.value.textContent = ''

    const interval = setInterval(() => {
      if (index < text.length && reasoningContent.value) {
        reasoningContent.value.textContent += text[index]
        reasoningContent.value.scrollTop = reasoningContent.value.scrollHeight
        index++
      } else {
        clearInterval(interval)
        resolve()
      }
    }, speed)
  })
}

const runAutoLoop = async () => {
  // Trigger extension icon click animation
  if (clickRipple.value) {
    clickRipple.value.classList.add('animate')
  }

  await new Promise(resolve => setTimeout(resolve, 300))

  if (popup.value) {
    popup.value.classList.add('show')
  }
  await new Promise(resolve => setTimeout(resolve, 800))

  if (textarea.value) {
    await typeText(textarea.value, promptText, 30)
  }
  await new Promise(resolve => setTimeout(resolve, 500))

  // Add generating class to all form fields for breathing effect
  const formFields = [title, name, phone, category, price, address, description]
  formFields.forEach(ref => {
    if (ref.value) {
      ref.value.classList.add('generating')
    }
  })

  setState('generating')
  await new Promise(resolve => setTimeout(resolve, 1500))

  setState('thinking')
  showReasoningBubble()

  const reasoningText = "Analyzing the rental listing form... The user wants to post a 2-bedroom apartment for rent. Let me extract the key information from their message: location is 123 Main Street in downtown, near subway; contact person is Michael Chen with phone (*************; rent is $2000/month; available next month. I'll create an attractive title, organize the contact details properly, and write a comprehensive description highlighting the apartment's location advantages and features..."
  await streamReasoningText(reasoningText, 35)
  await new Promise(resolve => setTimeout(resolve, 800))

  hideReasoningBubble()
  await new Promise(resolve => setTimeout(resolve, 300))

  setState('finish')

  // Fill all fields instantly (form fields still have generating class)
  fillField(title, formData['title'])
  fillField(name, formData['name'])
  fillField(phone, formData['phone'])
  fillField(category, formData['category'])
  fillField(price, formData['price'])
  fillField(address, formData['address'])
  fillField(description, formData['description'])

  // Wait a bit, then remove generating class from all form fields
  await new Promise(resolve => setTimeout(resolve, 500))
  formFields.forEach(ref => {
    if (ref.value) {
      ref.value.classList.remove('generating')
    }
  })

  // Wait a bit more to show the filled state
  await new Promise(resolve => setTimeout(resolve, 1500))

  setState('generate')
  if (textarea.value) textarea.value.value = ''

  // Clear all form fields
  formFields.forEach(ref => {
    if (ref.value) ref.value.value = ''
  })

  if (popup.value) {
    popup.value.classList.remove('show')
  }

  // Reset click ripple
  if (clickRipple.value) {
    clickRipple.value.classList.remove('animate')
  }

  await new Promise(resolve => setTimeout(resolve, 1500))

  runAutoLoop()
}

const createConfetti = () => {
  const canvas = confettiCanvas.value
  if (!canvas || !popup.value) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  const rect = popup.value.getBoundingClientRect()
  canvas.width = rect.width
  canvas.height = rect.height

  const confettiConfig = {
    confettiCount: 20,
    colors: [
      { front: '#3B82F6', back: '#2563EB' },
      { front: '#60A5FA', back: '#3B82F6' },
      { front: '#93C5FD', back: '#60A5FA' }
    ]
  }

  const confetti: any[] = []

  for (let i = 0; i < confettiConfig.confettiCount; i++) {
    confetti.push({
      x: canvas.width / 2,
      y: canvas.height / 2,
      vx: (Math.random() - 0.5) * 10,
      vy: Math.random() * -10 - 5,
      color: confettiConfig.colors[Math.floor(Math.random() * confettiConfig.colors.length)].front,
      size: Math.random() * 6 + 4
    })
  }

  const animate = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    confetti.forEach((particle, index) => {
      particle.x += particle.vx
      particle.y += particle.vy
      particle.vy += 0.3

      ctx.fillStyle = particle.color
      ctx.fillRect(particle.x, particle.y, particle.size, particle.size)

      if (particle.y > canvas.height + 100) {
        confetti.splice(index, 1)
      }
    })

    if (confetti.length > 0) {
      requestAnimationFrame(animate)
    }
  }

  animate()
}

const handleExtensionClick = () => {
  if (clickRipple.value) {
    // Reset animation
    clickRipple.value.classList.remove('animate')
    void clickRipple.value.offsetWidth // Force reflow
    clickRipple.value.classList.add('animate')

    // Show popup after ripple animation starts
    setTimeout(() => {
      if (popup.value) {
        popup.value.classList.add('show')
      }
    }, 200)
  }
}

const togglePopup = () => {
  if (popup.value) {
    popup.value.classList.toggle('show')
  }
}

const hidePopup = () => {
  if (popup.value) {
    popup.value.classList.remove('show')
  }
}

const startDemo = () => {
  // Manual trigger for demo
}

// Watch for shouldStartAnimation prop change
watch(() => props.shouldStartAnimation, (shouldStart) => {
  if (shouldStart && !animationTimeout) {
    animationTimeout = setTimeout(() => {
      runAutoLoop()
    }, 1000)
  }
})

onMounted(() => {
  // Don't auto-start animation on mount
  // Animation will start when shouldStartAnimation becomes true
})

onUnmounted(() => {
  if (animationTimeout) {
    clearTimeout(animationTimeout)
  }
})
</script>

<style scoped>
.full-chrome-demo-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0; /* Remove all padding to fit container exactly */
  background: transparent;
  pointer-events: none; /* Make entire container non-interactive */
}

/* Chrome Browser Frame - Fill parent container */
.chrome-browser {
  width: 100%;
  height: 100%;
  background: white;
  overflow: hidden;
  border-radius: 10px;
  outline: 0.50px #A6A6A6 solid;
  outline-offset: -0.50px;
  display: flex;
  flex-direction: column;
  position: relative;
  transform-origin: center center;
  isolation: isolate;
}

/* Reset all text alignment inside chrome browser */
.chrome-browser * {
  text-align: initial;
  pointer-events: none; /* Disable all interactions inside the browser */
  user-select: none; /* Disable text selection */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Tablet: maintain 16:10 ratio with right padding */
@media (max-width: 1440px) and (min-width: 857px) {
  .full-chrome-demo-container {
    padding: 2.5vh 16px 2.5vh 0; /* Add 16px right padding for tablet */
  }
}

/* At 856px and below: stop scaling, start translating left */
@media (max-width: 856px) {
  .full-chrome-demo-container {
    overflow: hidden;
    width: 100%;
    padding: 1.5vh 16px 1.5vh 0; /* No left padding, 16px right (matches container) */
    justify-content: flex-start;
  }

  .chrome-browser {
    /* Fixed size: 856px × 535px (16:10 ratio) */
    width: 856px;
    height: 535px;
    max-width: none; /* Override the max-width */
    aspect-ratio: auto; /* Override aspect-ratio */
    flex-shrink: 0;
    /* When wide enough: center it */
    /* When narrow: shift left to keep right edge visible with 16px margin */
    transform: translateX(max(calc(100vw - 856px - 16px), 0px));
    transform-origin: left center;
  }
}

/* When viewport narrower: shift left to show popup (right side) */
@media (max-width: 815px) {
  .chrome-browser {
    /* Ensure right edge stays 16px from viewport edge */
    transform: translateX(calc(100vw - 856px - 16px));
  }
}

/* Extra small: maintain 16px right margin */
@media (max-width: 640px) {
  .chrome-browser {
    /* Keep right edge 16px from viewport edge */
    transform: translateX(calc(100vw - 856px - 16px));
  }
}

/* Chrome Top Bar */
.chrome-topbar {
  height: 42px;
  background: #DFE1E5;
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 21px;
}

.chrome-dots {
  display: flex;
  gap: 8px;
  align-items: center;
}

.chrome-dot {
  width: 12px;
  height: 12px;
  border-radius: 6px;
}

.chrome-dot.red { background: #EC6B5E; }
.chrome-dot.yellow { background: #F4BF4F; }
.chrome-dot.green { background: #61C453; }

/* Chrome Tab Bar */
.chrome-tabbar {
  position: absolute;
  left: 87px;
  top: 0;
  height: 42px;
  display: flex;
  align-items: flex-end;
  padding-bottom: 0;
  overflow: visible;
}

.chrome-tab {
  width: 240px;
  height: 34px;
  position: relative;
  background: white;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  overflow: visible;
}

.chrome-tab-fade {
  width: 44px;
  height: 34px;
  position: absolute;
  right: 0;
  top: 0;
  background: linear-gradient(270deg, white 59%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
  border-top-right-radius: 8px;
}

.chrome-tab-close {
  width: 8px;
  height: 8px;
  position: absolute;
  right: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chrome-tab-favicon {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;
}

.chrome-tab-title-wrapper {
  flex: 1;
  overflow: hidden;
}

.chrome-tab-title {
  color: #3D4043;
  font-size: 12px;
  font-family: Roboto, sans-serif;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chrome-new-tab-btn {
  position: absolute;
  left: 350px;
  width: 12px;
  height: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chrome-window-controls {
  position: absolute;
  right: 21px;
  width: 10px;
  height: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Chrome Address Bar */
.chrome-addressbar {
  height: 36px;
  background: white;
  display: flex;
  align-items: center;
  padding: 0 16px;
  gap: 16px;
  position: relative;
}

.chrome-back-btn,
.chrome-forward-btn,
.chrome-refresh-btn {
  width: 14px;
  height: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.chrome-url-bar {
  flex: 1;
  height: 28px;
  background: #F1F3F4;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  padding: 0 14px;
  gap: 8px;
  position: relative;
}

.chrome-lock-icon {
  width: 8px;
  height: 11px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chrome-url-text-wrapper {
  flex: 1;
  overflow: hidden;
}

.chrome-url {
  color: #606367;
  font-size: 14px;
  font-family: Roboto, sans-serif;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chrome-star-icon,
.chrome-share-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chrome-star-icon {
  width: 14px;
  height: 13px;
}

.chrome-share-icon {
  width: 12px;
  height: 15px;
}

.chrome-extension-icon,
.chrome-profile-icon,
.chrome-menu-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chrome-extension-icon {
  width: 18px;
  height: 18px;
  cursor: pointer;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: transform 0.2s;
}

.chrome-extension-icon:hover {
  transform: scale(1.05);
}

.chrome-extension-icon:active {
  transform: scale(0.95);
}

.click-ripple {
  position: absolute;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid rgba(28, 91, 238, 0.4);
  pointer-events: none;
  opacity: 0;
  transform: scale(0.5);
}

.click-ripple.animate {
  animation: ripple-expand 0.6s ease-out forwards;
}

@keyframes ripple-expand {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  30% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: scale(1.5);
  }
}

.chrome-profile-icon {
  width: 18px;
  height: 18px;
}

.chrome-menu-icon {
  width: 3px;
  height: 14px;
}

.chrome-divider {
  height: 1px;
  background: #DBDCDD;
}

/* Chrome Content Area */
.chrome-content {
  flex: 1;
  background: linear-gradient(to bottom, #EFF6FF 0%, #FFFFFF 100%);
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* Simulated Webpage */
.webpage-content {
  width: 100%;
  max-width: 750px;
}

/* Main Form Card */
.webpage-form {
  background: white;
  border-radius: 12px;
  padding: 28px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Form Sections */
.form-section {
  margin-bottom: 20px;
}

.form-section:last-of-type {
  margin-bottom: 0;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  border-bottom: 1px solid #E5E7EB;
  padding-bottom: 8px;
  margin-bottom: 14px;
}

/* Form Layout */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 14px;
  margin-bottom: 14px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 14px;
}

.form-field:last-child {
  margin-bottom: 0;
}

.form-field.full-width {
  grid-column: 1 / -1;
}

.form-field label {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.form-field input,
.form-field textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #D1D5DB;
  border-radius: 6px;
  font-size: 13px;
  font-family: inherit;
  background: white;
  transition: all 0.2s;
  color: #111827;
}

.form-field input:focus,
.form-field textarea:focus {
  outline: none;
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-field input::placeholder,
.form-field textarea::placeholder {
  color: #9CA3AF;
}

.form-field textarea {
  resize: none;
  line-height: 1.5;
  min-height: 100px;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #E5E7EB;
}

.reset-button {
  padding: 8px 18px;
  border: 1px solid #D1D5DB;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
}

.reset-button:hover {
  background: #F9FAFB;
  border-color: #9CA3AF;
}

.submit-button {
  padding: 8px 18px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  color: white;
  background: #2563EB;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.submit-button:hover {
  background: #1D4ED8;
}

.submit-button svg {
  width: 14px;
  height: 14px;
}

/* Popup Container */
.fillify-popup {
  position: absolute;
  bottom: -400px;
  right: 40px;
  width: 300px;
  min-height: 292px;
  background: rgba(245, 245, 245, 0.92);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 13px;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  z-index: 100;
  opacity: 0;
  transition: bottom 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.fillify-popup.show {
  bottom: 20px;
  opacity: 1;
}

/* Header */
.fillify-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 16px;
}

.fillify-header h1 {
  margin: 0;
  color: #2563EB;
  font-size: 17px;
  font-weight: 700;
}

.fillify-header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fillify-settings-button {
  padding: 6px;
  border-radius: 3px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background 0.2s;
}

.fillify-settings-button:hover {
  background: rgba(0, 0, 0, 0.05);
}

#fillify-close-btn {
  padding: 3px;
  border-radius: 3px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background 0.2s;
}

#fillify-close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Form Container */
.fillify-form-container {
  background: white;
  border-radius: 13px;
  margin: 0 9px 9px 9px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  box-shadow: 0 3px 16px rgba(0, 0, 0, 0.08);
}

/* Mode Buttons */
.fillify-mode-buttons {
  position: relative;
  display: flex;
  gap: 3px;
  padding: 3px;
  background: #f5f5f5;
  border-radius: 6px;
}

.fillify-mode-buttons::after {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  width: calc(50% - 5px);
  height: calc(100% - 6px);
  background: white;
  border-radius: 5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 0;
}

.fillify-mode-btn {
  flex: 1;
  padding: 6px 9px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 11px;
  cursor: pointer;
  border-radius: 5px;
  position: relative;
  z-index: 1;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.fillify-mode-btn.active {
  color: #2563EB;
  font-weight: 500;
}

/* Textarea */
.fillify-textarea {
  width: 100%;
  padding: 11px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 11px;
  line-height: 1.5;
  resize: none;
  font-family: inherit;
  height: 174px;
  text-align: left;
}

.fillify-textarea:focus {
  outline: none;
  border-color: #3B82F6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Primary Button */
.fillify-primary-btn {
  width: 100%;
  padding: 6px;
  border: none;
  border-radius: 6px;
  background: #2563EB;
  color: white;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;
}

.fillify-primary-btn:hover:not(.success):not(.loading):not(.thinking):not(.stopping):not(:disabled) {
  background: #1D4ED8;
}

/* Button States */
.fillify-primary-btn.loading {
  background: #1D4ED8;
  cursor: not-allowed;
}

.fillify-primary-btn.thinking {
  background: #1D4ED8;
  cursor: not-allowed;
}

.fillify-primary-btn.stopping {
  background: #dc2626;
  cursor: not-allowed;
}

.fillify-primary-btn.success {
  background: #2563EB;
  cursor: default;
}

/* Sparkle Icon */
.fillify-sparkle-icon {
  width: 13px;
  height: 13px;
  color: inherit;
  opacity: 0.9;
  animation: sparkle 2s ease-in-out infinite;
  margin-left: 3px;
  transition: opacity 0.3s ease;
}

.fillify-sparkle-icon.hidden {
  opacity: 0;
  pointer-events: none;
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

/* Loading Animation */
.fillify-animation {
  display: none;
  position: absolute;
  border-radius: 100%;
  animation: ripple 0.6s linear infinite;
}

.fillify-primary-btn.loading .fillify-animation,
.fillify-primary-btn.thinking .fillify-animation {
  display: block;
}

@keyframes ripple {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.1),
               0 0 0 40px rgba(255, 255, 255, 0.1),
               0 0 0 80px rgba(255, 255, 255, 0.1),
               0 0 0 120px rgba(255, 255, 255, 0.1);
  }
  100% {
    box-shadow: 0 0 0 40px rgba(255, 255, 255, 0.1),
               0 0 0 80px rgba(255, 255, 255, 0.1),
               0 0 0 120px rgba(255, 255, 255, 0.1),
               0 0 0 160px rgba(255, 255, 255, 0);
  }
}

/* Stopping Animation (Red Ripple) */
.fillify-primary-btn.stopping .fillify-animation {
  animation: ripple-stop 0.6s linear infinite;
}

@keyframes ripple-stop {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.2),
               0 0 0 40px rgba(255, 255, 255, 0.2),
               0 0 0 80px rgba(255, 255, 255, 0.2),
               0 0 0 120px rgba(255, 255, 255, 0.2);
  }
  100% {
    box-shadow: 0 0 0 40px rgba(255, 255, 255, 0.2),
               0 0 0 80px rgba(255, 255, 255, 0.2),
               0 0 0 120px rgba(255, 255, 255, 0.2),
               0 0 0 160px rgba(255, 255, 255, 0);
  }
}

/* Reasoning Bubble */
.fillify-reasoning-card {
  position: absolute;
  right: 40px;
  bottom: 360px;
  width: 300px;
  height: 118px;
  border-radius: 12px;
  overflow: hidden;
  opacity: 0;
  transform: scale(0.8) translateY(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  pointer-events: auto;
  z-index: 101;
}

.fillify-reasoning-card.show {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.fillify-reasoning-blob {
  position: absolute;
  z-index: 1;
  width: 118px;
  height: 118px;
  border-radius: 50%;
  background: #3B82F6;
  opacity: 1;
  filter: blur(12px);
  animation: fillify-blob-bounce 3s linear infinite;
  will-change: transform;
}

@keyframes fillify-blob-bounce {
  0%, 100% { transform: translate(-50%, -50%); top: 0%; left: 0%; }
  25%      { transform: translate(-50%, -50%); top: 0%; left: 100%; }
  50%      { transform: translate(-50%, -50%); top: 100%; left: 100%; }
  75%      { transform: translate(-50%, -50%); top: 100%; left: 0%; }
}

.fillify-reasoning-bg {
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  border-radius: 8px;
  outline: 2px solid white;
  padding: 10px 13px;
}

.fillify-reasoning-close {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 19px;
  height: 19px;
  border: none;
  background: transparent;
  color: #9CA3AF;
  font-size: 16px;
  cursor: pointer;
  z-index: 3;
  transition: color 0.2s;
}

.fillify-reasoning-close:hover {
  color: #374151;
}

.fillify-reasoning-content {
  font-size: 10px;
  line-height: 1.4;
  color: #4B5563;
  height: 79px;
  overflow-y: auto;
  margin-top: 5px;
  padding-right: 5px;
}

/* Field Generating Effect - Breathing animation */
.form-field input.generating,
.form-field textarea.generating {
  border-color: #3B82F6;
  animation: field-breathing 2s ease-in-out infinite;
}

@keyframes field-breathing {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.2);
  }
  50% {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.4);
  }
}

/* Confetti Canvas */
.fillify-confetti-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}
</style>
