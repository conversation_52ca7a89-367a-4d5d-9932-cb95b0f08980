<template>
  <div class="page-wrapper">
    <!-- Global Background - Light Blue Theme -->
    <div class="page-background">
      <!-- Base gradient -->
      <div class="base-gradient"></div>

      <!-- Accent gradients -->
      <div class="accent-gradient accent-1"></div>
      <div class="accent-gradient accent-2"></div>
      <div class="accent-gradient accent-3"></div>
      <div class="noise-overlay"></div>
    </div>
    
    <TheHeader />
    
    <main class="flex-grow relative z-10">
      <slot />
    </main>

    <TheFooter />
    <StructuredData />
  </div>
</template>

<script setup lang="ts">
import StructuredData from '~/components/layout/StructuredData.vue'
</script>

<style scoped>
.page-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  overflow: hidden;
  pointer-events: none;
  background-color: #ffffff;
}

.base-gradient {
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at 50% 0%, rgba(240, 245, 255, 0.8) 0%, rgba(255, 255, 255, 0) 50%),
    linear-gradient(to bottom, #ffffff 0%, #f8fafc 100%);
}

.noise-overlay {
  position: absolute;
  inset: 0;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.05'/%3E%3C/svg%3E");
  opacity: 0.4;
  mix-blend-mode: overlay;
}

.accent-gradient {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.6;
  animation: float-accent 20s ease-in-out infinite;
}

.accent-1 {
  top: -10%;
  right: -5%;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
  animation-delay: 0s;
}

.accent-2 {
  bottom: 10%;
  left: -10%;
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  animation-delay: -5s;
}

.accent-3 {
  top: 40%;
  left: 20%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(168, 85, 247, 0.08) 0%, rgba(255, 255, 255, 0) 70%);
  animation-delay: -10s;
}

@keyframes float-accent {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -30px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.95);
  }
}
</style>