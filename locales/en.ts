export default {
  $locale: {
    name: 'English',
    nativeName: 'English'
  },
  welcome: 'Welcome to Fillify',
  description: 'AI-Powered Form Filling',
  nav: {
    home: 'Home',
    blog: 'Blog',
    signin: 'Sign in',
    dashboard: 'Dashboard',
    signout: 'Sign out',
    startFree: 'Start Free',
    language: 'Language'
  },
  hero: {
    chromeStore: 'Now Available on Chrome Web Store',
    edgeStore: 'Now Available on Microsoft Edge Add-ons',
    title: {
      text: 'Fill Forms Instantly with ',
      staticWord: 'Intelligent AI',
      rotatingWords: {
        0: 'Smart Automation',
        1: 'Perfect Precision',
        2: 'Seamless Integration',
        3: 'Future Technology',
        4: 'AI-Powered Magic'
      }
    },
    description: 'Just type one sentence, and AI will instantly fill any web form. The smartest way to handle online forms.',
    cta: {
      chrome: 'Add to Chrome',
      edge: 'Add to Edge',
      learnMore: 'Learn More'
    },
    badges: {
      title: 'Trusted by popular platforms'
    }
  },
  stats: {
    forms: {
      value: '+',
      label: 'Forms Filled Daily'
    },
    accuracy: {
      value: '%',
      label: 'Accuracy Rate'
    },
    support: {
      value: '24/7',
      label: 'AI Support'
    }
  },
  features: {
    title: 'Experience the Power of AI',
    subtitle: 'Discover how Fillify transforms your daily workflow with intelligent automation',
    formFilling: {
      title: 'Smart Form Detection',
      description: 'Fillify instantly recognizes and analyzes any web form on the page. Our advanced AI scans form fields, understands their purpose, and prepares them for intelligent auto-completion. From simple contact forms to complex multi-step applications, Fillify detects it all.',
      alt: 'Screenshot showing Fillify automatically detecting and highlighting form fields on a webpage',
      demoTitle: 'Smart Form Detection',
      fieldsDetected: 'fields detected'
    },
    email: {
      title: 'Smart & Custom Modes',
      description: 'Experience our AI-powered automation with Smart mode for intelligent auto-detection or switch to Custom mode to specifically tailor form filling, email composition, or bug reporting. Choose the approach that best fits your task.',
      alt: 'Interface showing Smart and Custom modes with corresponding options',
      demoTitle: 'Experience the Modes',
      promptLabel: 'Describe what you need',
      promptPlaceholder: 'Type your request here...',
      generate: 'Generate',
      generating: 'Generating...',
      label: 'Mode',
      promptExample: 'Fill out this job application form with my resume information including work experience at Tech Corp.',
      emailPromptExample: 'Write a professional follow-up email to the client about the project timeline and deliverables.',
      bugPromptExample: 'Create a bug report for login page error: users cannot login after password reset.'
    },
    bugReport: {
      title: 'Instant Auto-Fill Magic',
      description: 'Watch as AI transforms your simple description into perfectly formatted content and automatically fills every form field. No more tedious typing or copy-pasting. Just describe what you need in plain English, and Fillify handles the rest with lightning speed and perfect accuracy.',
      alt: 'Animation showing AI-generated content being automatically filled into form fields in real-time',
      demoTitle: 'AI Auto-Fill in Action',
      filling: 'AI is filling the form...',
      completed: 'Form completed successfully'
    },
    aiProvider: {
      title: 'Your AI, Your Choice, Your Security',
      description: 'Complete freedom to choose your preferred AI provider. Support for OpenAI, Claude, Gemini, and local models via Ollama. Your API keys stay secure in your browser, and with local model support, your sensitive data never leaves your device. Privacy-first design meets enterprise-grade security.',
      alt: 'Secure interface showing multiple AI provider options with emphasis on local models and privacy',
      demoTitle: 'Multiple AI Providers',
      secureLocal: 'Supports Local Models',
      privacyFirst: 'Privacy First',
      localModelDesc: 'Connect to Ollama for local AI models - your data never leaves your device'
    },
    modes: {
      demoTitle: 'Experience Our Modes',
      promptLabel: 'Describe what you need',
      promptPlaceholder: 'Type your request here...',
      generate: 'Generate',
      generating: 'Generating...',
      smartPrompt: 'Just describe what you need, and AI will intelligently fill any form, compose emails, or create detailed bug reports automatically.',
      smartPlaceholder: 'Experience AI-powered smart automation',
      emailPlaceholder: 'Enter email content description',
      bugPlaceholder: 'Enter bug description',
      smartTab: 'Smart',
      customTab: 'Custom',
      generalLabel: 'General',
      emailLabel: 'Email',
      bugLabel: 'Bug Report'
    },
    autoFill: {
      demoTitle: 'AI Auto-Fill in Action',
      filling: 'AI is filling the form...',
      completed: 'Form completed successfully'
    }
  },
  faq: {
    title: 'Frequently Asked Questions',
    items: {
      what: {
        question: 'What is Fillify?',
        answer: 'Fillify is an AI-powered browser extension that helps you fill out forms, compose emails, and create detailed bug reports with a single click by leveraging advanced AI models to understand your needs and automatically complete fields, saving you time and effort on repetitive tasks.'
      },
      types: {
        question: 'What types of forms can Fillify handle?',
        answer: 'Fillify works with various forms, including general web forms, bug reports, and emails. It supports text fields, text areas, and more, ensuring seamless automation across different websites.'
      },
      providers: {
        question: 'Which AI providers does Fillify support?',
        answer: 'Fillify supports multiple leading AI providers such as OpenAI\'s GPT models, Anthropic\'s Claude series, Google\'s Gemini models, DeepSeek, Moonshot, and locally-run AI models through Ollama integration, giving you maximum flexibility and choice over how content is generated.'
      },
      privacy: {
        question: 'How does Fillify protect my data and privacy?',
        answer: "Fillify never sends your API keys or form data to our servers—your keys are stored securely in your browser and all requests go directly from your device to the AI provider. This means only you and the chosen provider ever see your data. For even stronger control, Fillify supports Ollama integration so you can connect to locally-run AI models, ensuring maximum security and privacy."
      },
      customize: {
        question: 'Can I customize AI responses for specific forms?',
        answer: 'Yes! In bug report mode, you can create custom templates with predefined information to help generate more accurate and consistent bug reports.'
      },
      languages: {
        question: 'What languages does Fillify support?',
        answer: 'Fillify supports multiple languages and can automatically detect the form\'s language. You can also manually select your preferred output language in the extension popup.'
      }
    }
  },
  bottomCta: {
    subtitle: 'Ready to Transform Your Workflow?',
    title: 'Experience the Future of Form Filling Today',
    button: 'Install Now'
  },
  footer: {
    copyright: '© {year} Fillify. All rights reserved.',
    social: {
      twitter: 'X (Twitter)',
      youtube: 'YouTube'
    },
    links: {
      terms: 'Terms of Service',
      privacy: 'Privacy Policy'
    }
  },
  signin: {
    title: 'Welcome to Fillify',
    subtitle: 'Sign in to use our AI-powered form filling extension',
    email: {
      label: 'Email Address',
      placeholder: 'Enter your email address',
      continue: 'Continue with email',
      sending: 'Sending...'
    },
    otp: {
      sentMessage: 'We\'ve sent a verification code to',
      label: 'Login Code',
      placeholder: 'Enter verification code',
      continue: 'Continue with login code',
      verifying: 'Verifying...',
      resend: {
        resending: 'Resending...',
        cooldown: 'Resend in {seconds}s',
        action: 'Resend code'
      }
    },
    google: {
      continue: 'Continue with Google',
      loading: 'Redirecting...'
    },
    divider: 'or',
    error: {
      invalidEmail: 'Please enter a valid email address',
      sendOTP: 'Failed to send verification code. Please try again.',
      invalidOTP: 'Please enter a valid 6-digit verification code',
      verifyOTP: 'Invalid or expired verification code. Please try again.',
      googleLogin: 'Google login failed. Please try again.'
    },
    features: {
      title: 'What you\'ll get:',
      list: {
        autoFill: 'AI-Powered Form Auto-Fill',
        api: 'Customize with Your Own API',
        early: 'Early Access to New Features'
      }
    },
    terms: {
      prefix: 'By signing in, you agree to our',
      and: 'and',
      termsOfService: 'Terms of Service',
      privacyPolicy: 'Privacy Policy'
    },
    seo: {
      title: 'Sign in - Fillify',
      description: 'Sign in to Fillify to access AI-powered form filling features'
    },
    meta: {
      title: 'Sign in - Fillify',
      description: 'Sign in to Fillify to access AI-powered form filling features and manage your account.'
    }
  },
  seo: {
    description: 'Fillify revolutionizes form filling with AI technology. Automatically complete web forms, compose emails, and generate bug reports with intelligent automation.'
  },
  meta: {
    title: 'Fillify – AI-Powered Automatic Form Filler',
    description: 'Fillify is an AI-Powered Automatic Form Filler that completes any online form instantly. Save time with one-click autofill powered by AI.',
    keywords: {
      formFilling: 'AI Form Filler',
      automation: 'Automatic Form Filler',
      email: 'AI Email Assistant',
      bugReport: 'AI Bug Report Generator',
      additional: [
        'Smart Form Autofill',
        'Automated Data Entry',
        'AI Form Assistant',
        'Intelligent Form Filling',
        'Chrome Form Autofill',
        'One-Click Form Filler'
      ]
    }
  },
  privacy: {
    meta: {
      title: 'Privacy Policy - Fillify',
      description: 'Learn about how Fillify protects your privacy and handles your data.'
    },
    title: 'Privacy Policy',
    lastUpdated: 'Last updated: {date}'
  },
  terms: {
    meta: {
      title: 'Terms of Service - Fillify',
      description: 'Read about the terms and conditions for using Fillify services.'
    },
    title: 'Terms of Service',
    lastUpdated: 'Last updated: {date}'
  },
  dashboard: {
    meta: {
      title: 'Dashboard - Fillify',
      description: 'Manage your Fillify account, view your current plan, and track usage.'
    },
    currentPlan: 'Current Plan',
    settings: 'Settings',
    usageOverview: 'Usage Overview',
    creditsUsed: 'Credits Used'
  },
  blog: {
    meta: {
      title: 'Blog - Fillify',
      description: 'Read the latest news, updates, and tips about AI-powered form filling and productivity automation.'
    },
    hero: {
      badge: 'Latest Updates',
      title: 'Blog',
      subtitle: 'Latest news, releases, and tips'
    },
    list: {
      readMore: 'Read More',
      publishedOn: 'Published on',
      minRead: 'min read',
      noPostsTitle: 'No posts yet',
      noPostsDescription: 'We\'re working on creating great content. Please check back soon.'
    },
    article: {
      backToBlog: 'Back to Blog',
      thanksTitle: 'Thanks for reading!',
      thanksDescription: 'If you have any questions or suggestions about Fillify, feel free to contact us.',
      tryFillify: 'Try Fillify',
      moreArticles: 'More Articles',
      notFoundTitle: 'Article Not Found',
      notFoundDescription: 'Sorry, the article you\'re looking for doesn\'t exist or has been removed.',
      backToBlogBtn: 'Back to Blog'
    }
  },
  demo: {
    meta: {
      title: 'Demo Form - Test Fillify Auto-Fill',
      description: 'Try out Fillify\'s AI-powered form filling capabilities with this interactive demo form. See how AI can transform your form filling experience.'
    }
  },
  success: {
    meta: {
      title: 'Order Successful - Fillify',
      description: 'Thank you for your order! Your Fillify subscription is now active.'
    }
  },
  cancel: {
    meta: {
      title: 'Order Cancelled - Fillify',
      description: 'Your order has been cancelled. We\'re sorry to see you go.'
    }
  },
  refund: {
    meta: {
      title: 'Refund Policy - Fillify',
      description: 'Learn about Fillify\'s refund policy and how to request a refund for your subscription.'
    }
  },

  '404': {
    title: 'Page Not Found',
    description: 'Sorry, we couldn\'t find the page you\'re looking for. Please check the URL or return to the homepage.',
    backHome: 'Back to Home'
  },
  auth: {
    callback: {
      processing: 'Processing authentication...'
    }
  },
  faqPage: {
    meta: {
      title: 'Frequently Asked Questions - Fillify',
      description: 'Find answers to common questions about Fillify, the AI-powered tool that automatically fills forms, composes emails, and generates bug reports.'
    },
    title: 'Frequently Asked Questions',
    subtitle: 'Get answers to common questions about Fillify\'s AI-powered form filling, email composition, and bug report generation tools.',
    sections: {
      what: {
        title: 'What is Fillify?',
        content: 'Fillify is an AI-powered browser extension that revolutionizes how you interact with web forms, emails, and bug reports. Our intelligent solution automatically fills forms, composes emails, and generates bug reports with minimal user input, saving you hours of manual work. Using advanced AI technology, Fillify understands context and fills fields accurately and efficiently.'
      },
      types: {
        title: 'What types of forms can Fillify fill?',
        content: 'Fillify is designed to handle a wide variety of form types, including contact and inquiry forms, job application forms, registration and sign-up forms, survey and questionnaire forms, e-commerce checkout forms, support ticket forms, feedback and review forms, and custom business forms. Our AI technology adapts to different form structures and field types to provide accurate and efficient form completion.'
      },
      providers: {
        title: 'Which AI providers does Fillify use?',
        content: 'Fillify supports multiple AI providers to give you flexibility and choice: OpenAI\'s GPT models, Anthropic\'s Claude models, Google\'s Gemini models, Mistral AI models, and custom AI models. You can switch between different AI providers based on your needs, privacy preferences, and performance requirements. Each provider offers unique strengths for different use cases.'
      },
      privacy: {
        title: 'How does Fillify ensure privacy and security?',
        content: 'Privacy and security are our top priorities at Fillify: We don\'t store your personal data on our servers, all form data processing happens locally in your browser, communication with AI providers is encrypted, we comply with GDPR and other privacy regulations, you have full control over what information is shared with AI providers, and we use minimal data necessary for form completion. Your sensitive information remains yours at all times.'
      },
      customize: {
        title: 'Can I customize Fillify\'s behavior?',
        content: 'Absolutely! Fillify offers extensive customization options: Create custom templates for frequently used information, configure AI response formats and styles, set up personal profiles with your data, adjust privacy settings for data sharing, choose specific AI providers for different tasks, and configure form field mapping preferences. These customizations help Fillify work exactly the way you need it to.'
      },
      languages: {
        title: 'What languages does Fillify support?',
        content: 'Fillify supports multiple languages for both interface and content processing: English, Spanish, French, German, Japanese, Korean, Chinese (Simplified and Traditional), and Russian. The AI models can understand and generate content in these languages, making Fillify useful for international users and multilingual forms.'
      },
      gettingStarted: {
        title: 'How do I get started with Fillify?',
        content: 'Getting started with Fillify is simple: Install the extension from the Chrome Web Store or Microsoft Edge Add-ons, create a free account or sign in if you already have one, configure your AI provider preferences, set up your personal profile with information you frequently use, and start using Fillify on any web form, email, or bug report. You\'ll be saving time with AI-powered automation in just a few minutes!'
      },
      accuracy: {
        title: 'How accurate is Fillify\'s AI?',
        content: 'Fillify\'s AI accuracy is consistently high due to our advanced machine learning algorithms that understand context, semantics, and specific form requirements. Our system achieves high accuracy by learning from patterns in your input data and adapting to different form types and fields. We continuously improve our AI models based on user feedback and new developments in natural language processing technology.'
      }
    },
    cta: {
      title: 'Still have questions?',
      description: 'If you can\'t find the answer to your question, feel free to contact us directly.',
      button: 'Contact Us'
    }
  },
  howItWorks: {
    meta: {
      title: 'How Fillify Works - AI-Powered Form Filling Guide',
      description: 'Learn how Fillify\'s AI-powered browser extension works to automatically fill forms, compose emails, and generate bug reports. Simple 6-step process to save hours every week.'
    },
    title: 'How Fillify Works',
    subtitle: 'Learn how our AI-powered browser extension transforms your online form-filling experience with just a few simple steps.',
    steps: {
      install: {
        title: 'Install the Extension',
        description: 'Download and install Fillify from the Chrome Web Store or Microsoft Edge Add-ons. The extension integrates seamlessly with your browser.',
        features: [
          'One-click installation',
          'No additional software needed',
          'Works with existing browser'
        ]
      },
      configure: {
        title: 'Configure AI Provider',
        description: 'Connect your preferred AI provider (OpenAI, Claude, Gemini, etc.) by adding your API key. You maintain full control over your data.',
        features: [
          'Multiple AI provider support',
          'Secure API key storage',
          'Easy switching between providers'
        ]
      },
      describe: {
        title: 'Describe Your Needs',
        description: 'When you encounter a form, email, or bug report, describe what you want to fill in natural language. Fillify\'s AI understands your instructions.',
        features: [
          'Natural language processing',
          'Context-aware completion',
          'Adaptable to any form type'
        ]
      },
      aiFill: {
        title: 'AI Fills Automatically',
        description: 'Fillify analyzes the form structure and intelligently fills fields based on your description. You can review and edit before submitting.',
        features: [
          'Smart field detection',
          'Contextual understanding',
          'Real-time completion'
        ]
      },
      review: {
        title: 'Review and Submit',
        description: 'Check the filled information, make any necessary adjustments, and submit with confidence knowing the work was completed quickly and accurately.',
        features: [
          'Human review step',
          'Easy editing capabilities',
          'Secure submission'
        ]
      },
      saveTime: {
        title: 'Save Time & Focus',
        description: 'Experience the freedom of spending your time on important tasks while Fillify handles the repetitive form-filling tasks for you.',
        features: [
          'Save hours each week',
          'Focus on important work',
          'Reduce repetitive tasks'
        ]
      }
    },
    technology: {
      title: 'The Technology Behind Fillify',
      sections: {
        aiAnalysis: {
          title: 'AI-Powered Analysis',
          description: 'Fillify uses advanced machine learning models to understand form structures, field types, and relationships between different inputs. This allows for accurate completion even in complex forms.'
        },
        nlp: {
          title: 'Natural Language Processing',
          description: 'Describe what you want in plain English, and Fillify\'s NLP engine translates your instructions into accurate form completions, understanding context and requirements.'
        },
        privacy: {
          title: 'Privacy-First Design',
          description: 'All processing happens in your browser, and your data never touches our servers. Your information stays private and secure while using AI to complete forms.'
        },
        adaptive: {
          title: 'Adaptive Learning',
          description: 'Fillify learns from your usage patterns and preferences, becoming more accurate and efficient at completing forms according to your specific needs over time.'
        }
      }
    },
    cta: {
      title: 'Ready to Transform Your Form-Filling Experience?',
      description: 'Join thousands of users who save hours every week with Fillify\'s AI-powered form completion.',
      button: 'Get Started Free'
    }
  }
}