export default {
  $locale: {
    name: 'Chinese',
    nativeName: '简体中文'
  },
  welcome: '欢迎使用 Fillify',
  description: 'AI 智能表单填写助手',
  nav: {
    home: '首页',
    blog: '博客',
    signin: '登录',
    dashboard: '控制台',
    signout: '退出登录',
    startFree: '免费体验',
    language: '语言'
  },
  hero: {
    chromeStore: '现已上架 Chrome 扩展应用商店',
    edgeStore: '现已上架 Microsoft Edge 扩展应用商店',
    title: {
      text: '用智能 AI 技术，',
      staticWord: '几秒填完任何表单',
      rotatingWords: {
        0: 'AI 黑科技',
        1: '智能自动化',
        2: '未来新体验',
        3: '精准无误',
        4: '无缝集成'
      }
    },
    description: '只需输入一句话，AI 即可秒填任何网页表单，让填写变得简单、高效。',
    cta: {
      chrome: '添加至 Chrome',
      edge: '添加至 Edge',
      learnMore: '了解详情'
    },
    badges: {
      title: '荣誉展示'
    }
  },
  stats: {
    forms: {
      value: '+',
      label: '每日自动填写表单'
    },
    accuracy: {
      value: '%',
      label: '填写准确率'
    },
    support: {
      value: '24/7',
      label: '全天候 AI 支持'
    }
  },
  features: {
    title: '体验 AI 的力量',
    subtitle: '探索 Fillify 如何通过智能自动化改变您的日常工作流程',
    formFilling: {
      title: '智能表单识别',
      description: 'Fillify 瞬间识别并分析网页上的任何表单。我们的先进 AI 扫描表单字段，理解其用途，并为智能自动填写做好准备。从简单的联系表单到复杂的多步骤申请，Fillify 都能精准识别。',
      alt: '展示 Fillify 自动检测并高亮显示网页表单字段的截图',
      demoTitle: '智能表单检测',
      fieldsDetected: '个字段已检测'
    },
    email: {
      title: '智能与自定义模式',
      description: '体验我们由AI驱动的自动化，使用智能模式进行智能自动检测，或切换到自定义模式以专门定制表单填写、邮件撰写或错误报告。选择最适合您任务的方法。',
      alt: '显示智能和自定义模式及其相应选项的界面',
      demoTitle: '体验这些模式',
      promptLabel: '描述您的需求',
      promptPlaceholder: '在此输入您的请求...',
      generate: '生成',
      generating: '生成中...',
      label: '模式',
      promptExample: '用我的简历信息填写这份工作申请表，包括在科技公司的工作经验。',
      emailPromptExample: '写一封专业的跟进邮件给客户，说明项目时间表和交付成果。',
      bugPromptExample: '创建一个错误报告：用户在密码重置后无法登录。'
    },
    bugReport: {
      title: 'AI 自动填写体验',
      description: '观看 AI 如何将您的简单描述转换为完美格式的内容，并自动填写每个表单字段。无需繁琐的输入或复制粘贴。只需用简单的中文描述您的需求，Fillify 就能以闪电般的速度和完美的准确性处理其余工作。',
      alt: '展示 AI 生成的内容实时自动填入表单字段的动画',
      demoTitle: 'AI 自动填写实况',
      filling: 'AI 正在填写表单...',
      completed: '表单填写完成'
    },
    aiProvider: {
      title: '您的 AI，您的选择，您的安全',
      description: '完全自由选择您偏好的 AI 提供商。支持 OpenAI、Claude、Gemini 以及通过 Ollama 部署的本地模型。您的 API 密钥安全存储在浏览器中，使用本地模型意味着您的敏感数据永远不会离开您的设备。隐私优先设计，企业级安全保障。',
      alt: '显示多个 AI 提供商选项的安全界面，强调本地模型和隐私保护',
      demoTitle: '多个 AI 提供商',
      secureLocal: '支持本地模型',
      privacyFirst: '隐私优先',
      localModelDesc: '连接至 Ollama 本地 AI 模型 - 您的数据永不离开设备'
    }
  },
  faq: {
    title: '常见问题',
    items: {
      what: {
        question: 'Fillify 是什么？',
        answer: 'Fillify 是一款由 AI 驱动的浏览器扩展程序，可以帮助您一键填写表单、撰写邮件和创建详细的错误报告。借助先进的 AI 模型，它能理解您的需求并自动填写各个字段，为您节省处理重复性任务的时间和精力。'
      },
      types: {
        question: 'Fillify 可以处理哪些类型的表单？',
        answer: 'Fillify 适用于各种表单，包括通用网页表单、错误报告和电子邮件。支持文本字段、文本区域等多种元素，确保在不同网站上都能实现无缝的自动化体验。'
      },
      providers: {
        question: 'Fillify 支持哪些 AI 供应商？',
        answer: 'Fillify 支持多个领先的 AI 供应商，包括 OpenAI 的 GPT 模型、Anthropic 的 Claude 系列、Google 的 Gemini 模型、DeepSeek、Moonshot 以及通过 Ollama 部署的本地 AI 模型，为您提供内容生成方式的最大灵活性和选择。'
      },
      privacy: {
        question: 'Fillify 如何保护我的数据和隐私？',
        answer: 'Fillify 绝不会将您的 API 密钥或表单数据发送至我们的服务器 - 您的密钥在浏览器中安全存储，所有请求都直接从您的设备发送到您选择的 AI 供应商。这意味着只有您和所选的供应商才能看到您的数据。为了更强大的控制，Fillify 支持与 Ollama 集成，让您能够连接到本地部署的 AI 模型，确保最高水平的安全性和隐私性。'
      },
      customize: {
        question: '我能否针对特定表单自定义 AI 回应？',
        answer: '可以！在错误报告模式中，您可以创建含有预设信息的自定义模板，以帮助生成更精确且一致的错误报告。'
      },
      languages: {
        question: 'Fillify 支持哪些语言？',
        answer: 'Fillify 支持多种语言，并能自动检测表单语言。您也可以在扩展程序弹出窗口中手动选择偏好的输出语言。'
      }
    }
  },
  bottomCta: {
    subtitle: '让 AI 释放您的生产力',
    title: '立即体验 智能表单填写',
    button: '立即安装'
  },
  footer: {
    copyright: '© {year} Fillify。保留所有权利。',
    social: {
      twitter: 'X（推特）',
      youtube: 'YouTube'
    },
    links: {
      terms: '服务条款',
      privacy: '隐私政策'
    }
  },
  signin: {
    title: '欢迎使用 Fillify',
    subtitle: '登录后，即可体验 AI 智能填写功能',
    email: {
      label: '邮箱地址',
      placeholder: '请输入您的邮箱地址',
      continue: '使用邮箱继续',
      sending: '发送中...'
    },
    otp: {
      sentMessage: '我们已向以下邮箱发送验证码',
      label: '登录验证码',
      placeholder: '请输入验证码',
      continue: '使用验证码登录',
      verifying: '验证中...',
      resend: {
        resending: '重新发送中...',
        cooldown: '{seconds}秒后重新发送',
        action: '重新发送验证码'
      }
    },
    google: {
      continue: '使用 Google 登录',
      loading: '跳转中...'
    },
    divider: '或',
    error: {
      invalidEmail: '请输入有效的邮箱地址',
      sendOTP: '发送验证码失败，请重试。',
      invalidOTP: '请输入有效的6位验证码',
      verifyOTP: '验证码无效或已过期，请重试。',
      googleLogin: 'Google 登录失败，请重试。'
    },
    features: {
      title: '登录后，您将享受：',
      list: {
        autoFill: 'AI 智能表单填写',
        api: '支持自定义 AI API',
        early: '抢先体验新功能'
      }
    },
    terms: {
      prefix: '登录即表示您同意我们的',
      and: '和',
      termsOfService: '服务条款',
      privacyPolicy: '隐私政策'
    },
    seo: {
      title: '登录 - Fillify',
      description: '登录 Fillify，体验 AI 自动表单填写功能。'
    },
    meta: {
      title: '登录 - Fillify',
      description: '登录 Fillify 以访问 AI 驱动的表单填写功能并管理您的账户。'
    }
  },
  meta: {
    title: 'Fillify – AI 驱动的自动表单填充工具',
    description: 'Fillify 是一款 AI 驱动的自动表单填充工具，可瞬间完成任何在线表单。通过 AI 驱动的一键自动填充功能节省时间。',
    keywords: {
      formFilling: 'AI 表单填充',
      automation: '自动表单填充',
      email: 'AI 邮件助手',
      bugReport: 'AI 错误报告生成器',
      additional: [
        '智能表单自动填充',
        '自动化数据录入',
        'AI 表单助手',
        '智能表单填写',
        'Chrome 表单自动填充',
        '一键表单填充'
      ]
    }
  },
  privacy: {
    meta: {
      title: '隐私政策 - Fillify',
      description: '了解 Fillify 如何保护您的数据和隐私信息。'
    },
    title: '隐私政策',
    lastUpdated: '最近更新：{date}'
  },
  terms: {
    meta: {
      title: '服务条款 - Fillify',
      description: '阅读 Fillify 的服务条款和使用协议。'
    },
    title: '服务条款',
    lastUpdated: '最近更新：{date}'
  },
  dashboard: {
    meta: {
      title: '控制台 - Fillify',
      description: '管理您的 Fillify 账户，查看当前套餐和使用情况。'
    },
    currentPlan: '当前套餐',
    settings: '设置',
    usageOverview: '使用概览',
    creditsUsed: '已用额度'
  },
  blog: {
    meta: {
      title: '博客 - Fillify',
      description: '阅读最新的AI表单填写技术文章、产品更新和生产力自动化技巧。'
    },
    hero: {
      badge: '最新动态',
      title: '博客',
      subtitle: '最新资讯、产品发布和使用技巧'
    },
    list: {
      readMore: '阅读更多',
      publishedOn: '发布于',
      minRead: '分钟阅读',
      noPostsTitle: '暂无文章',
      noPostsDescription: '我们正在创作优质内容，请稍后再来查看。'
    },
    article: {
      backToBlog: '返回博客',
      thanksTitle: '感谢阅读！',
      thanksDescription: '如果您对 Fillify 有任何问题或建议，欢迎联系我们。',
      tryFillify: '体验 Fillify',
      moreArticles: '更多文章',
      notFoundTitle: '文章未找到',
      notFoundDescription: '抱歉，您要查找的文章不存在或已被移除。',
      backToBlogBtn: '返回博客'
    }
  },
  demo: {
    meta: {
      title: '演示表单 - 体验 Fillify 自动填写',
      description: '通过这个交互式演示表单体验 Fillify 的 AI 自动填写功能，感受 AI 如何改变您的表单填写体验。'
    }
  },
  success: {
    meta: {
      title: '订单成功 - Fillify',
      description: '感谢您的订单！您的 Fillify 订阅现已激活。'
    }
  },
  cancel: {
    meta: {
      title: '订单取消 - Fillify',
      description: '您的订单已被取消。很遗憾看到您离开。'
    }
  },
  refund: {
    meta: {
      title: '退款政策 - Fillify',
      description: '了解 Fillify 的退款政策以及如何申请订阅退款。'
    }
  },

  '404': {
    title: '页面未找到',
    description: '抱歉，我们找不到您要访问的页面，请检查URL或返回首页。',
    backHome: '返回首页'
  },
  auth: {
    callback: {
      processing: '正在处理认证...'
    }
  },
  faqPage: {
    meta: {
      title: '常见问题 - Fillify',
      description: '查找关于 Fillify 的常见问题答案，这是一款自动填写表单、撰写邮件和生成错误报告的 AI 驱动工具。'
    },
    title: '常见问题',
    subtitle: '获取关于 Fillify 的 AI 驱动表单填写、邮件撰写和错误报告生成工具的常见问题解答。',
    sections: {
      what: {
        title: 'Fillify 是什么？',
        content: 'Fillify 是一款由 AI 驱动的浏览器扩展程序，彻底改变您与网页表单、电子邮件和错误报告的交互方式。我们智能的解决方案只需最少的用户输入即可自动填写表单、撰写邮件和生成错误报告，为您节省数小时的手动工作时间。使用先进的 AI 技术，Fillify 能够理解上下文并准确、高效地填写字段。'
      },
      types: {
        title: 'Fillify 可以填写哪些类型的表单？',
        content: 'Fillify 旨在处理各种类型的表单，包括联系和查询表单、求职申请表、注册和注册表、调查和问卷表、电子商务结账表、支持工单、反馈和评论表单以及自定义业务表单。我们的 AI 技术能够适应不同的表单结构和字段类型，以提供准确和高效的表单填写。'
      },
      providers: {
        title: 'Fillify 使用哪些 AI 提供商？',
        content: 'Fillify 支持多个 AI 提供商，为您提供灵活性和选择：OpenAI 的 GPT 模型、Anthropic 的 Claude 模型、Google 的 Gemini 模型、Mistral AI 模型和自定义 AI 模型。您可以根据需求、隐私偏好和性能要求在不同的 AI 提供商之间切换。每个提供商都为不同的用例提供独特的功能。'
      },
      privacy: {
        title: 'Fillify 如何确保隐私和安全？',
        content: '隐私和安全是我们 Fillify 的首要任务：我们不会将您的个人数据存储在我们的服务器上，所有表单数据处理都在您的浏览器中进行，与 AI 提供商的通信是加密的，我们遵守 GDPR 和其他隐私法规，您可以完全控制与 AI 提供商共享的信息，并且我们仅使用表单填写所需的最少数据。您的敏感信息始终归您所有。'
      },
      customize: {
        title: '我可以自定义 Fillify 的行为吗？',
        content: '当然可以！Fillify 提供广泛的自定义选项：为常用信息创建自定义模板，配置 AI 响应格式和样式，使用您的数据设置个人资料，调整数据共享的隐私设置，为不同任务选择特定的 AI 提供商，以及配置表单字段映射偏好。这些自定义功能帮助 Fillify 完全按照您的需求工作。'
      },
      languages: {
        title: 'Fillify 支持哪些语言？',
        content: 'Fillify 支持多种语言的界面和内容处理：英语、西班牙语、法语、德语、日语、韩语、中文（简体和繁体）和俄语。AI 模型可以理解并生成这些语言的内容，使 Fillify 对国际用户和多语言表单非常有用。'
      },
      gettingStarted: {
        title: '我如何开始使用 Fillify？',
        content: '开始使用 Fillify 很简单：从 Chrome 网上应用店或 Microsoft Edge 加载项安装扩展程序，创建一个免费账户或登录（如果已有账户），配置您的 AI 提供商首选项，使用您经常使用的信息设置您的个人资料，然后在任何网页表单、电子邮件或错误报告上开始使用 Fillify。您只需几分钟就能通过 AI 驱动的自动化节省时间！'
      },
      accuracy: {
        title: 'Fillify 的 AI 有多准确？',
        content: 'Fillify 的 AI 准确性始终保持在高水平，这得益于我们先进的机器学习算法，这些算法能够理解上下文、语义和特定的表单要求。我们的系统通过学习您输入数据中的模式并适应不同的表单类型和字段来实现高准确性。我们根据用户反馈和自然语言处理技术的新发展不断改进我们的 AI 模型。'
      }
    },
    cta: {
      title: '还有其他问题？',
      description: '如果您找不到问题的答案，请随时直接联系我们。',
      button: '联系我们'
    }
  },
  howItWorks: {
    meta: {
      title: 'Fillify 工作原理 - AI 驱动表单填写指南',
      description: '了解 Fillify 的 AI 驱动浏览器扩展程序如何自动填写表单、撰写邮件和生成错误报告。每周只需几个简单步骤节省数小时。'
    },
    title: 'Fillify 工作原理',
    subtitle: '了解我们的 AI 驱动浏览器扩展程序如何仅需几个简单步骤就能改变您的在线表格填写体验。',
    steps: {
      install: {
        title: '安装扩展程序',
        description: '从 Chrome 网上应用店或 Microsoft Edge 加载项下载并安装 Fillify。扩展程序与您的浏览器无缝集成。',
        features: [
          '一键安装',
          '无需额外软件',
          '适用于现有浏览器'
        ]
      },
      configure: {
        title: '配置 AI 提供商',
        description: '通过添加 API 密钥连接您首选的 AI 提供商（OpenAI、Claude、Gemini 等）。您可以完全控制您的数据。',
        features: [
          '支持多个 AI 提供商',
          '安全的 API 密钥存储',
          '轻松在提供商之间切换'
        ]
      },
      describe: {
        title: '描述您的需求',
        description: '当您遇到表单、邮件或错误报告时，用自然语言描述您想要填写的内容。Fillify 的 AI 理解您的指令。',
        features: [
          '自然语言处理',
          '上下文感知完成',
          '适用于任何表单类型'
        ]
      },
      aiFill: {
        title: 'AI 自动填写',
        description: 'Fillify 分析表单结构并根据您的描述智能填写字段。您可以在提交前查看和编辑。',
        features: [
          '智能字段检测',
          '上下文理解',
          '实时完成'
        ]
      },
      review: {
        title: '检查并提交',
        description: '检查填写的信息，进行任何必要的调整，然后在确认工作已完成快速准确后提交。',
        features: [
          '人工检查步骤',
          '轻松编辑功能',
          '安全提交'
        ]
      },
      saveTime: {
        title: '节省时间并专注',
        description: '体验将时间花在重要任务上的自由，同时让 Fillify 处理重复的表单填写任务。',
        features: [
          '每周节省数小时',
          '专注于重要工作',
          '减少重复任务'
        ]
      }
    },
    technology: {
      title: 'Fillify 背后的技术',
      sections: {
        aiAnalysis: {
          title: 'AI 驱动分析',
          description: 'Fillify 使用先进的机器学习模型来理解表单结构、字段类型和不同输入之间的关系。这允许在复杂表单中也能准确完成。'
        },
        nlp: {
          title: '自然语言处理',
          description: '用简单的中文描述您想要的内容，Fillify 的 NLP 引擎将您的指令转换为准确的表单填写，理解上下文和要求。'
        },
        privacy: {
          title: '隐私优先设计',
          description: '所有处理都在您的浏览器中进行，您的数据永远不会触及我们的服务器。在使用 AI 完成表单时，您的信息保持私密和安全。'
        },
        adaptive: {
          title: '自适应学习',
          description: 'Fillify 从您的使用模式和偏好中学习，随着时间的推移变得更加准确和高效，根据您的特定需求完成表单。'
        }
      }
    },
    cta: {
      title: '准备好改变您的表单填写体验了吗？',
      description: '加入成千上万的用户，他们每周通过 Fillify 的 AI 驱动表单填写节省数小时。',
      button: '免费开始'
    }
  }
}