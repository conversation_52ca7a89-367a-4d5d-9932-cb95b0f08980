export default {
  $locale: {
    name: 'Korean',
    nativeName: '한국어'
  },
  welcome: 'Fillify에 오신 것을 환영합니다',
  description: 'AI 기반 자동 양식 작성',
  nav: {
    home: '홈',
    blog: '블로그',
    signin: '로그인',
    dashboard: '대시보드',
    signout: '로그아웃',
    startFree: '무료로 시작하기',
    language: '언어'
  },
  hero: {
    chromeStore: 'Chrome 웹 스토어에서 만나보세요',
    edgeStore: 'Microsoft Edge 애드온에서 만나보세요',
    title: {
      text: '몇 초 만에 양식 작성 완료,',
      staticWord: '스마트 AI 기술로',
      rotatingWords: {
        0: 'AI의 마법 같은 기술',
        1: '스마트 자동화',
        2: '미래형 테크놀로지',
        3: '완벽한 정확도',
        4: '끊김 없는 통합'
      }
    },
    description: '한 문장만 입력하면 AI가 웹 양식을 즉시 채워줍니다. 온라인 양식을 다루는 가장 스마트한 방법입니다.',
    cta: {
      chrome: 'Chrome에 추가',
      edge: 'Edge에 추가',
      learnMore: '자세히 알아보기'
    }
  },
  stats: {
    forms: {
      value: '+',
      label: '일일 자동 작성된 양식'
    },
    accuracy: {
      value: '%',
      label: '정확도'
    },
    support: {
      value: '24/7',
      label: 'AI 실시간 지원'
    }
  },
  features: {
    title: 'AI의 혁신적인 기능을 경험하세요',
    subtitle: 'Fillify가 자동화를 통해 어떻게 업무 효율을 극대화하는지 확인해 보세요',
    formFilling: {
      title: '스마트 양식 감지',
      description: 'Fillify는 페이지의 모든 웹 양식을 즉시 인식하고 분석합니다. 고급 AI가 양식 필드를 스캔하고 용도를 파악하여 지능적인 자동 완성을 준비합니다. 간단한 연락처 양식부터 복잡한 다단계 신청서까지 모든 것을 정확하게 감지합니다.',
      alt: 'Fillify가 웹페이지의 양식 필드를 자동으로 감지하고 하이라이트하는 스크린샷',
      demoTitle: '스마트 양식 감지',
      fieldsDetected: '개 필드 감지됨'
    },
    email: {
      title: '스마트 및 커스텀 모드',
      description: '스마트 모드로 지능형 자동 탐지를 경험하거나 커스텀 모드로 전환하여 폼 작성, 이메일 작성 또는 버그 보고를 전문적으로 맞춤 설정하세요. 작업에 가장 적합한 접근 방식을 선택하세요.',
      alt: '스마트 모드와 커스텀 모드 및 해당 옵션들을 보여주는 인터페이스',
      demoTitle: '모드 체험',
      promptLabel: '필요한 내용을 설명하세요',
      promptPlaceholder: '여기에 요청사항을 입력하세요...',
      generate: '생성',
      generating: '생성 중...',
      label: '모드',
      promptExample: '내 이력 정보(테크 코프의 직무 경험 포함)로 이 구직 신청서를 작성해 주세요.',
      emailPromptExample: '프로젝트 일정 및 결과물에 관해 고객에게 전문적인 후속 이메일을 작성하세요.',
      bugPromptExample: '비밀번호 재설정 후 사용자가 로그인할 수 없는 로그인 페이지 오류에 대한 버그 보고서를 작성하세요.'
    },
    bugReport: {
      title: '즉시 자동 완성 마법',
      description: 'AI가 간단한 설명을 완벽하게 포맷된 콘텐츠로 변환하고 모든 양식 필드를 자동으로 채우는 과정을 지켜보세요. 번거로운 타이핑이나 복사-붙여넣기는 필요 없습니다. 한국어로 간단히 설명하기만 하면 Fillify가 번개같은 속도와 완벽한 정확성으로 나머지 작업을 처리합니다.',
      alt: 'AI 생성 콘텐츠가 실시간으로 양식 필드에 자동 입력되는 애니메이션',
      demoTitle: 'AI 자동 완성 실연',
      filling: 'AI가 양식을 작성 중...',
      completed: '양식 작성 완료'
    },
    aiProvider: {
      title: '당신의 AI, 당신의 선택, 당신의 보안',
      description: '선호하는 AI 제공업체를 완전히 자유롭게 선택할 수 있습니다. OpenAI, Claude, Gemini, 그리고 Ollama를 통한 로컬 모델을 지원합니다. API 키는 브라우저에 안전하게 저장되며, 로컬 모델 지원으로 민감한 데이터가 기기를 떠나지 않습니다. 프라이버시 우선 설계와 엔터프라이즈급 보안을 제공합니다.',
      alt: '로컬 모델과 프라이버시 보호를 강조한 여러 AI 제공업체 옵션을 보여주는 보안 인터페이스',
      demoTitle: '다중 AI 제공업체',
      secureLocal: '로컬 모델 지원',
      privacyFirst: '프라이버시 우선',
      localModelDesc: 'Ollama에 연결하여 로컬 AI 모델 사용 - 데이터가 기기를 떠나지 않음'
    }
  },
  faq: {
    title: '자주 묻는 질문',
    items: {
      what: {
        question: 'Fillify는 무엇인가요?',
        answer: 'Fillify는 AI 기반 브라우저 확장 프로그램으로, 한 번의 클릭으로 양식 작성, 이메일 작성을 지원하고, 세부적인 버그 리포트를 생성할 수 있습니다. 고급 AI 모델을 활용하여 사용자의 요구를 이해하고 필드를 자동으로 작성함으로써 반복적인 작업에 소요되는 시간과 노력을 절약할 수 있습니다.'
      },
      types: {
        question: 'Fillify는 어떤 유형의 양식을 처리할 수 있나요?',
        answer: 'Fillify는 일반 웹 양식, 버그 리포트, 이메일 등 다양한 양식을 지원합니다. 텍스트 필드, 텍스트 영역 등을 포함한 다양한 웹사이트에서 원활한 자동화를 보장합니다.'
      },
      providers: {
        question: 'Fillify는 어떤 AI 제공업체를 지원하나요?',
        answer: 'Fillify는 OpenAI의 GPT 모델, Anthropic의 Claude 시리즈, Google의 Gemini 모델, DeepSeek, Moonshot 및 Ollama 통합을 통한 로컬에서 실행되는 AI 모델 등 다양한 주요 AI 제공업체를 지원하여, 콘텐츠 생성 방식에 대한 최대한의 유연성과 선택권을 제공합니다.'
      },
      privacy: {
        question: 'Fillify는 내 데이터와 개인정보를 어떻게 보호하나요?',
        answer: 'Fillify는 사용자의 API 키나 양식 데이터를 서버로 전송하지 않습니다. 키는 브라우저에 안전하게 저장되며, 모든 요청은 사용자의 장치에서 직접 AI 제공업체로 전송됩니다. 즉, 귀하와 선택한 제공업체만이 데이터를 확인할 수 있습니다. 보다 강력한 통제를 위해 Fillify는 Ollama 통합을 지원하여 로컬에서 실행되는 AI 모델에 연결할 수 있도록 하여, 최고 수준의 보안과 개인정보 보호를 실현합니다.'
      },
      customize: {
        question: '특정 양식에 맞게 AI 응답을 맞춤 설정할 수 있나요?',
        answer: '네! 버그 리포트 모드에서는 사전 정의된 정보가 포함된 맞춤 템플릿을 생성하여 보다 정확하고 일관된 버그 리포트를 생성할 수 있습니다.'
      },
      languages: {
        question: 'Fillify는 어떤 언어를 지원하나요?',
        answer: 'Fillify는 여러 언어를 지원하며, 양식 언어를 자동으로 감지할 수 있습니다. 또한 확장 프로그램 팝업에서 원하는 출력 언어를 수동으로 선택할 수 있습니다.'
      }
    }
  },
  bottomCta: {
    subtitle: '업무 효율을 극대화할 준비가 되셨나요?',
    title: '지금 바로 AI 기반 자동 양식 작성의 혁신을 경험해 보세요',
    button: '지금 설치하기'
  },
  footer: {
    copyright: '© {year} Fillify. 모든 권리 보유.',
    social: {
      twitter: 'X (트위터)',
      youtube: '유튜브'
    },
    links: {
      terms: '이용 약관',
      privacy: '개인정보 보호정책'
    }
  },
  signin: {
    title: 'Fillify에 오신 것을 환영합니다',
    subtitle: 'AI 기반 자동 양식 작성 기능을 사용하려면 로그인하세요',
    email: {
      label: '이메일 주소',
      placeholder: '이메일 주소를 입력하세요',
      continue: '이메일로 계속하기',
      sending: '전송 중...'
    },
    otp: {
      sentMessage: '다음 주소로 인증 코드를 보냈습니다',
      label: '로그인 코드',
      placeholder: '인증 코드를 입력하세요',
      continue: '로그인 코드로 계속하기',
      verifying: '인증 중...',
      resend: {
        resending: '재전송 중...',
        cooldown: '{seconds}초 후 재전송',
        action: '인증 코드 재전송'
      }
    },
    google: {
      continue: 'Google로 계속하기',
      loading: '리디렉션 중...'
    },
    divider: '또는',
    error: {
      invalidEmail: '유효한 이메일 주소를 입력하세요',
      sendOTP: '인증 코드 전송에 실패했습니다. 다시 시도해 주세요.',
      invalidOTP: '유효한 6자리 인증 코드를 입력하세요',
      verifyOTP: '인증 코드가 유효하지 않거나 만료되었습니다. 다시 시도해 주세요.',
      googleLogin: 'Google 로그인에 실패했습니다. 다시 시도해 주세요.'
    },
    features: {
      title: '로그인하면 제공되는 기능:',
      list: {
        autoFill: 'AI 기반 자동 양식 작성',
        api: '맞춤 API 연결',
        early: '새로운 기능 사전 체험'
      }
    },
    terms: {
      prefix: '로그인하면 당사의',
      and: '및',
      termsOfService: '이용 약관',
      privacyPolicy: '개인정보 보호정책'
    },
    seo: {
      title: '로그인 - Fillify',
      description: 'Fillify에 로그인하여 AI 기반 자동 양식 작성 기능을 이용하세요'
    }
  },
  meta: {
    title: 'Fillify – AI 기반 자동 양식 작성기',
    description: 'Fillify는 AI 기반 자동 양식 작성기로, 어떤 온라인 양식이든 즉시 작성할 수 있습니다. AI 기반 원클릭 자동 완성으로 시간을 절약하세요.',
    keywords: {
      formFilling: 'AI 양식 작성기',
      automation: '자동 양식 작성',
      email: 'AI 이메일 어시스턴트',
      bugReport: 'AI 버그 리포트 생성기',
      additional: [
        '스마트 양식 자동 완성',
        '자동 데이터 입력',
        'AI 양식 어시스턴트',
        '지능형 양식 작성',
        'Chrome 양식 자동 완성',
        '원클릭 양식 작성기'
      ]
    }
  },
  privacy: {
    meta: {
      title: '개인정보 보호정책 - Fillify',
      description: 'Fillify가 개인정보를 보호하고 데이터를 처리하는 방식에 대해 알아보세요.'
    },
    title: '개인정보 보호정책',
    lastUpdated: '최종 업데이트: {date}'
  },
  terms: {
    meta: {
      title: '이용 약관 - Fillify',
      description: 'Fillify 서비스 이용 약관을 확인하세요.'
    },
    title: '이용 약관',
    lastUpdated: '최종 업데이트: {date}'
  },
  dashboard: {
    meta: {
      title: '대시보드 - Fillify',
      description: 'Fillify 계정을 관리하고 현재 플랜을 확인하며 사용량을 추적하세요.'
    },
    currentPlan: '현재 플랜',
    settings: '설정',
    usageOverview: '사용량 개요',
    creditsUsed: '사용한 크레딧'
  },
  blog: {
    meta: {
      title: '블로그 - Fillify',
      description: 'AI 기반 양식 작성과 생산성 자동화에 대한 최신 뉴스, 업데이트, 팁을 읽어보세요.'
    },
    hero: {
      badge: '최신 업데이트',
      title: '블로그',
      subtitle: '최신 뉴스, 릴리스, 팁'
    },
    list: {
      readMore: '더 읽기',
      publishedOn: '게시일',
      minRead: '분 소요',
      noPostsTitle: '아직 게시물이 없습니다',
      noPostsDescription: '훌륭한 콘텐츠를 준비 중입니다. 잠시 후 다시 확인해 주세요.'
    },
    article: {
      backToBlog: '블로그로 돌아가기',
      thanksTitle: '읽어주셔서 감사합니다!',
      thanksDescription: 'Fillify에 대한 질문이나 제안이 있으시면 언제든지 연락해 주세요.',
      tryFillify: 'Fillify 사용해보기',
      moreArticles: '더 많은 글',
      notFoundTitle: '글을 찾을 수 없습니다',
      notFoundDescription: '죄송합니다. 찾으시는 글이 존재하지 않거나 삭제되었습니다.',
      backToBlogBtn: '블로그로 돌아가기'
    }
  },
  '404': {
    title: '페이지를 찾을 수 없습니다',
    description: '죄송하지만 찾으시는 페이지를 찾을 수 없습니다. URL을 확인하거나 홈페이지로 돌아가세요.',
    backHome: '홈으로 돌아가기'
  },
  demo: {
    meta: {
      title: '데모 폼 - Fillify 자동 입력 기능 체험',
      description: '이 인터랙티브 데모 폼으로 Fillify의 AI 기반 자동 입력 기능을 체험해 보세요. AI가 폼 입력 경험을 어떻게 바꾸는지 확인해 보세요.'
    }
  },
  success: {
    meta: {
      title: '주문 성공 - Fillify',
      description: '주문해 주셔서 감사합니다! Fillify 구독이 활성화되었습니다.'
    }
  },
  cancel: {
    meta: {
      title: '주문 취소 - Fillify',
      description: '주문이 취소되었습니다. 이용해 주셔서 감사합니다.'
    }
  },
  refund: {
    meta: {
      title: '환불 정책 - Fillify',
      description: 'Fillify의 환불 정책 및 구독 환불 신청 방법을 확인하세요.'
    }
  },
  auth: {
    callback: {
      processing: '인증 처리 중...'
    }
  },
  faqPage: {
    meta: {
      title: '자주 묻는 질문 - Fillify',
      description: '폼 자동 작성, 이메일 작성, 버그 리포트 생성을 하는 AI 기반 도구인 Fillify에 대한 일반적인 질문에 대한 답변을 찾아보세요.'
    },
    title: '자주 묻는 질문',
    subtitle: 'Fillify의 AI 기반 폼 작성, 이메일 작성, 버그 리포트 생성 도구에 대한 일반적인 질문에 대한 답변을 제공합니다.',
    sections: {
      what: {
        title: 'Fillify는 무엇인가요?',
        content: 'Fillify는 웹 폼, 이메일, 버그 리포트와의 상호작용 방식을 혁신하는 AI 기반 브라우저 확장 프로그램입니다. 지능형 솔루션을 통해 최소한의 사용자 입력으로 폼 자동 작성, 이메일 작성, 버그 리포트 생성을 수행하여 수 시간의 수작업을 절약할 수 있습니다. 고급 AI 기술을 사용하여 Fillify는 맥락을 이해하고 정확하고 효율적으로 필드를 작성합니다.'
      },
      types: {
        title: 'Fillify는 어떤 유형의 폼을 작성할 수 있나요?',
        content: 'Fillify는 연락처 및 문의 폼, 취업 지원 폼, 등록 및 가입 폼, 설문 조사 폼, 전자상거래 결제 폼, 지원 티켓, 피드백 및 리뷰 폼, 커스텀 비즈니스 폼 등 다양한 유형의 폼을 처리하도록 설계되었습니다. 당사의 AI 기술은 다양한 폼 구조와 필드 유형에 적응하여 정확하고 효율적인 폼 작성을 제공합니다.'
      },
      providers: {
        title: 'Fillify는 어떤 AI 제공업체를 사용하나요?',
        content: 'Fillify는 유연성과 선택지를 제공하기 위해 여러 AI 제공업체를 지원합니다: OpenAI의 GPT 모델, Anthropic의 Claude 모델, Google의 Gemini 모델, Mistral AI 모델 및 커스텀 AI 모델. 필요, 프라이버시 설정, 성능 요구 사항에 따라 다양한 AI 제공업체 간 전환할 수 있습니다. 각 제공업체는 다양한 사용 사례에 고유한 강점을 제공합니다.'
      },
      privacy: {
        title: 'Fillify는 개인정보 및 보안을 어떻게 보장하나요?',
        content: 'Fillify에서 개인정보와 보안이 최우선입니다: 당사는 서버에 개인 데이터를 저장하지 않으며, 모든 폼 데이터 처리는 브라우저에서 로컬로 수행됩니다. AI 제공업체와의 통신은 암호화되며, 당사는 GDPR 및 기타 개인정보 보호 규정을 준수합니다. AI 제공업체와 공유되는 정보에 대한 완전한 통제권을 보유하고 있으며, 폼 작성에 필요한 최소한의 데이터만 사용합니다. 민감한 정보는 항상 귀하의 소유입니다.'
      },
      customize: {
        title: 'Fillify의 동작을 커스터마이징할 수 있나요?',
        content: '물론입니다! Fillify는 광범위한 커스터마이징 옵션을 제공합니다: 자주 사용하는 정보에 대한 커스텀 템플릿 생성, AI 응답 형식 및 스타일 설정, 데이터가 포함된 개인 프로필 설정, 데이터 공유 개인정보 보호 설정 조정, 다양한 작업에 특정 AI 제공업체 선택, 폼 필드 매핑 기본 설정 설정 등. 이러한 커스터마이징 기능을 통해 Fillify는 필요에 따라 정확하게 동작합니다.'
      },
      languages: {
        title: 'Fillify는 어떤 언어를 지원하나요?',
        content: 'Fillify는 인터페이스 및 콘텐츠 처리를 위한 다국어를 지원합니다: 영어, 스페인어, 프랑스어, 독일어, 일본어, 한국어, 중국어(간체 및 번체), 러시아어. AI 모델은 이 언어들로 콘텐츠를 이해하고 생성할 수 있어 국제 사용자 및 다국어 폼에 유용합니다.'
      },
      gettingStarted: {
        title: 'Fillify를 시작하려면 어떻게 해야 하나요?',
        content: 'Fillify 시작은 간단합니다: Chrome 웹 스토어 또는 Microsoft Edge 애드온에서 확장 프로그램을 설치하고, 무료 계정을 만들거나 이미 있다면 로그인하고, AI 제공업체 기본 설정을 구성하고, 자주 사용하는 정보로 개인 프로필을 설정하고, 모든 웹 폼, 이메일 또는 버그 리포트에서 Fillify 사용을 시작합니다. 몇 분 안에 AI 기반 자동화로 시간을 절약할 수 있습니다!'
      },
      accuracy: {
        title: 'Fillify의 AI 정확도는 어느 정도인가요?',
        content: 'Fillify의 AI 정확도는 맥락, 의미 및 특정 폼 요구 사항을 이해하는 고급 기계 학습 알고리즘 덕분에 일관되게 높습니다. 당사 시스템은 입력 데이터의 패턴을 학습하고 다양한 폼 유형과 필드에 적응함으로써 높은 정확도를 달성합니다. 사용자 피드백 및 자연어 처리 기술의 새로운 발전을 기반으로 AI 모델을 지속적으로 개선합니다.'
      }
    },
    cta: {
      title: '그래도 질문이 있나요?',
      description: '질문에 대한 답변을 찾을 수 없는 경우, 언제든지 직접 문의해 주세요.',
      button: '문의하기'
    }
  },
  howItWorks: {
    meta: {
      title: 'Fillify 작동 방식 - AI 기반 폼 작성 가이드',
      description: 'Fillify의 AI 기반 브라우저 확장 프로그램이 폼 자동 작성, 이메일 작성, 버그 리포트 생성을 하는 방식을 알아보세요. 매주 수 시간을 절약하는 간단한 6단계 프로세스.'
    },
    title: 'Fillify 작동 방식',
    subtitle: '간단한 몇 단계만으로 온라인 폼 작성 경험을 바꾸는 AI 기반 브라우저 확장 프로그램의 작동 방식을 알아보세요.',
    steps: {
      install: {
        title: '확장 프로그램 설치',
        description: 'Chrome 웹 스토어 또는 Microsoft Edge 애드온에서 Fillify를 다운로드 및 설치합니다. 확장 프로그램은 브라우저와 시ーム리스하게 통합됩니다.',
        features: [
          '원클릭 설치',
          '추가 소프트웨어 필요 없음',
          '기존 브라우저에서 작동'
        ]
      },
      configure: {
        title: 'AI 제공업체 설정',
        description: 'API 키를 추가하여 선호하는 AI 제공업체(OpenAI, Claude, Gemini 등)를 연결합니다. 데이터에 대한 완전한 통제권을 유지합니다.',
        features: [
          '다중 AI 제공업체 지원',
          '보안 API 키 저장',
          '제공업체 간 쉬운 전환'
        ]
      },
      describe: {
        title: '필요 사항 설명',
        description: '폼, 이메일 또는 버그 리포트를 마주쳤을 때 자연어로 작성하고자 하는 내용을 설명하세요. Fillify의 AI는 명령을 이해합니다.',
        features: [
          '자연어 처리',
          '맥락 인식 작성',
          '모든 폼 유형에 적용 가능'
        ]
      },
      aiFill: {
        title: 'AI 자동 작성',
        description: 'Fillify는 폼 구조를 분석하고 설명을 기반으로 지능적으로 필드를 작성합니다. 제출하기 전에 검토 및 편집이 가능합니다.',
        features: [
          '스마트 필드 감지',
          '맥락 이해',
          '실시간 작성 완료'
        ]
      },
      review: {
        title: '검토 및 제출',
        description: '입력된 정보를 확인하고 필요한 조정을 하며, 작업이 빠르고 정확하게 완료되었음을 알고 제출할 수 있습니다.',
        features: [
          '인간 검토 단계',
          '쉬운 편집 기능',
          '보안 제출'
        ]
      },
      saveTime: {
        title: '시간 절약 및 집중',
        description: 'Fillify가 반복적인 폼 작성 작업을 처리하는 동안 중요한 작업에 시간을 할애할 수 있는 자유를 경험하세요.',
        features: [
          '매주 수 시간 절약',
          '중요 작업에 집중',
          '반복 작업 감소'
        ]
      }
    },
    technology: {
      title: 'Fillify의 기술',
      sections: {
        aiAnalysis: {
          title: 'AI 기반 분석',
          description: 'Fillify는 폼 구조, 필드 유형, 다양한 입력 간의 관계를 이해하는 고급 기계 학습 모델을 사용합니다. 복잡한 폼에서도 정확한 작성 기능을 제공합니다.'
        },
        nlp: {
          title: '자연어 처리',
          description: '간단한 한국어로 원하는 내용을 설명하시면, Fillify의 자연어 처리 엔진은 명령을 정확한 폼 작성으로 변환하며, 맥락과 요구 사항을 이해합니다.'
        },
        privacy: {
          title: '프라이버시 중심 설계',
          description: '모든 처리는 브라우저에서 이루어지며, 데이터는 당사 서버에 도달하지 않습니다. 폼 작성에 AI를 사용할 때도 정보는 개인적이고 안전하게 유지됩니다.'
        },
        adaptive: {
          title: '적응형 학습',
          description: 'Fillify는 사용 패턴과 기본 설정을 학습하여 시간이 지남에 따라 특정 요구에 따라 폼을 작성하는 데 더욱 정확하고 효율적이 됩니다.'
        }
      }
    },
    cta: {
      title: '폼 작성 경험을 바꿀 준비가 되셨나요?',
      description: 'Fillify의 AI 기반 폼 작성으로 매주 수 시간을 절약하는 수천 명의 사용자에 합류하세요.',
      button: '무료로 시작하기'
    }
  }
}