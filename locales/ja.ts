export default {
  $locale: {
    name: 'Japanese',
    nativeName: '日本語'
  },
  welcome: 'Fillifyへようこそ',
  description: 'AIによるフォーム自動入力',
  nav: {
    home: 'ホーム',
    blog: 'ブログ',
    signin: 'サインイン',
    dashboard: 'ダッシュボード',
    signout: 'サインアウト',
    startFree: '無料で始める',
    language: '言語'
  },
  hero: {
    chromeStore: 'Chrome ウェブストアで提供中',
    edgeStore: 'Microsoft Edge アドオンで提供中',
    title: {
      text: '数秒でフォームを完了、',
      staticWord: 'スマートAI技術で',
      rotatingWords: {
        0: 'AIの魔法',
        1: 'スマートオートメーション',
        2: '次世代テクノロジー',
        3: '完璧な精度',
        4: 'シームレスな統合'
      }
    },
    description: '一文入力するだけで、AIが瞬時にあらゆるウェブフォームを自動記入。オンラインフォームを最もスマートに処理する方法です。',
    cta: {
      chrome: 'Chrome に追加',
      edge: 'Edge に追加',
      learnMore: '詳しく見る'
    }
  },
  stats: {
    forms: {
      value: '+',
      label: '毎日記入されるフォーム数'
    },
    accuracy: {
      value: '%',
      label: '精度'
    },
    support: {
      value: '24/7',
      label: 'AIサポート'
    }
  },
  features: {
    title: 'AIの力を体験',
    subtitle: 'Fillifyのインテリジェントな自動化が、あなたのワークフローをどのように変えるのかをご紹介',
    formFilling: {
      title: 'スマートフォーム検出',
      description: 'Fillifyはページ上のあらゆるウェブフォームを瞬時に認識・分析します。先進的なAIがフォームフィールドをスキャンし、その目的を理解して、インテリジェントな自動入力の準備を行います。シンプルな連絡フォームから複雑な多段階申請まで、すべて検出可能です。',
      alt: 'Fillifyがウェブページのフォームフィールドを自動検出・ハイライト表示するスクリーンショット',
      demoTitle: 'スマートフォーム検出',
      fieldsDetected: 'フィールドを検出'
    },
    email: {
      title: 'スマートモードとカスタムモード',
      description: 'AI搭載の自動化を体験してください。スマートモードでスマート自動検出を使用するか、カスタムモードに切り替えて、フォーム入力、メール作成、またはバグ報告を専門的にカスタマイズできます。タスクに最適なアプローチを選択してください。',
      alt: 'スマートモードとカスタムモードおよび対応オプションを表示するインターフェース',
      demoTitle: 'モードを体験',
      promptLabel: '必要事項を記述してください',
      promptPlaceholder: 'こちらにリクエストを入力...',
      generate: '生成',
      generating: '生成中...',
      label: 'モード',
      promptExample: 'この求人応募フォームに私の履歴情報（テックコーポでの職務経験を含む）を入力してください。',
      emailPromptExample: 'プロジェクトのタイムラインと成果物についてクライアントにプロフェッショナルなフォローアップメールを書いてください。',
      bugPromptExample: 'パスワードリセット後にユーザーがログインできないというログインページのエラーのバグレポートを作成してください。'
    },
    bugReport: {
      title: '瞬間自動入力マジック',
      description: 'AIがシンプルな説明を完璧にフォーマットされたコンテンツに変換し、すべてのフォームフィールドを自動入力する様子をご覧ください。面倒な入力やコピー＆ペーストは不要。日本語で簡単に説明するだけで、Fillifyが驚異的なスピードと完璧な精度で残りの作業を処理します。',
      alt: 'AI生成コンテンツがリアルタイムでフォームフィールドに自動入力されるアニメーション',
      demoTitle: 'AI自動入力実演',
      filling: 'AIがフォームを入力中...',
      completed: 'フォーム入力完了'
    },
    aiProvider: {
      title: 'あなたのAI、あなたの選択、あなたのセキュリティ',
      description: 'お好みのAIプロバイダーを完全に自由に選択できます。OpenAI、Claude、Gemini、そしてOllamaによるローカルモデルをサポート。APIキーはブラウザに安全に保存され、ローカルモデルサポートにより、機密データがデバイスから外部に送信されることはありません。プライバシー第一の設計とエンタープライズグレードのセキュリティ。',
      alt: 'ローカルモデルとプライバシー保護を強調した複数のAIプロバイダーオプションを表示するセキュアなインターフェース',
      demoTitle: '複数のAIプロバイダー',
      secureLocal: 'ローカルモデル対応',
      privacyFirst: 'プライバシー第一',
      localModelDesc: 'Ollamaに接続してローカルモデルを使用 - データはデバイスから外部に送信されません'
    }
  },
  faq: {
    title: 'よくある質問',
    items: {
      what: {
        question: 'Fillifyとは？',
        answer: 'Fillifyは、AIを活用したブラウザ拡張機能で、フォーム入力、メール作成、詳細なバグレポートの作成を1クリックで実現できます。高度なAIモデルを活用し、あなたのニーズを理解してフィールドを自動的に入力することで、繰り返しのタスクにかかる時間と労力を節約します。'
      },
      types: {
        question: 'Fillifyはどんなフォームに対応していますか？',
        answer: 'Fillifyは、一般的なWebフォーム、バグレポート、メールなど、さまざまなフォームに対応しています。テキストフィールド、テキストエリアなどに対応し、異なるWebサイトでシームレスな自動化を実現します。'
      },
      providers: {
        question: 'FillifyはどのAIプロバイダーをサポートしていますか？',
        answer: 'Fillifyは、OpenAIのGPTモデル、AnthropicのClaudeシリーズ、GoogleのGeminiモデル、DeepSeek、Moonshot、およびOllamaとの統合を通じたローカルで実行されるAIモデルなど、複数の主要AIプロバイダーをサポートしています。これにより、コンテンツ生成方法について最大限の柔軟性と選択肢を提供します。'
      },
      privacy: {
        question: 'Fillifyはデータとプライバシーをどのように保護しますか？',
        answer: 'Fillifyは、APIキーまたはフォームデータを決してサーバーに送信しません。キーはブラウザに安全に保存され、すべてのリクエストはデバイスから直接AIプロバイダーに送信されます。つまり、データを閲覧できるのはあなたと選択したプロバイダーのみです。さらに強固な管理を実現するため、FillifyはOllamaとの統合をサポートしており、ローカルで実行されるAIモデルに接続できるため、最高のセキュリティとプライバシーを実現します。'
      },
      customize: {
        question: '特定のフォームに合わせてAIの応答をカスタマイズできますか？',
        answer: 'はい！バグレポートモードでは、あらかじめ定義された情報を含むカスタムテンプレートを作成し、より正確で一貫性のあるバグレポートを生成できます。'
      },
      languages: {
        question: 'Fillifyはどの言語をサポートしていますか？',
        answer: 'Fillifyは複数の言語をサポートしており、フォームの言語を自動的に検出できます。また、拡張機能のポップアップで出力言語を手動で選択することも可能です。'
      }
    }
  },
  bottomCta: {
    subtitle: 'ワークフローを変革する準備はできましたか？',
    title: '次世代のフォーム入力を体験しよう',
    button: '今すぐインストール'
  },
  footer: {
    copyright: '© {year} Fillify. All rights reserved.',
    social: {
      twitter: 'X（Twitter）',
      youtube: 'YouTube'
    },
    links: {
      terms: '利用規約',
      privacy: 'プライバシーポリシー'
    }
  },
  signin: {
    title: 'Fillifyへようこそ',
    subtitle: 'AIフォーム入力拡張機能を利用するにはサインインしてください',
    email: {
      label: 'メールアドレス',
      placeholder: 'メールアドレスを入力してください',
      continue: 'メールアドレスで続ける',
      sending: '送信中...'
    },
    otp: {
      sentMessage: '以下のメールアドレスに確認コードを送信しました',
      label: 'ログインコード',
      placeholder: '確認コードを入力してください',
      continue: 'ログインコードで続ける',
      verifying: '確認中...',
      resend: {
        resending: '再送信中...',
        cooldown: '{seconds}秒後に再送信',
        action: '確認コードを再送信'
      }
    },
    google: {
      continue: 'Googleで続ける',
      loading: 'リダイレクト中...'
    },
    divider: 'または',
    error: {
      invalidEmail: '有効なメールアドレスを入力してください',
      sendOTP: '確認コードの送信に失敗しました。もう一度お試しください。',
      invalidOTP: '6桁の有効な確認コードを入力してください',
      verifyOTP: '確認コードが無効または期限切れです。もう一度お試しください。',
      googleLogin: 'Googleログインに失敗しました。もう一度お試しください。'
    },
    features: {
      title: 'サインインするとできること:',
      list: {
        autoFill: 'AIによるフォーム自動入力',
        api: '独自のAPIカスタマイズ',
        early: '新機能への先行アクセス'
      }
    },
    terms: {
      prefix: 'サインインすることで、',
      and: 'および',
      termsOfService: '利用規約',
      privacyPolicy: 'プライバシーポリシー'
    },
    seo: {
      title: 'サインイン - Fillify',
      description: 'AIによるフォーム入力機能を利用するためにFillifyにサインイン'
    }
  },
  meta: {
    title: 'Fillify – AI搭載の自動フォーム入力ツール',
    description: 'FillifyはAI搭載の自動フォーム入力ツールで、あらゆるオンラインフォームを即座に完了できます。AI搭載のワンクリック自動入力で時間を節約できます。',
    keywords: {
      formFilling: 'AIフォーム入力ツール',
      automation: '自動フォーム入力',
      email: 'AIメールアシスタント',
      bugReport: 'AIバグレポートジェネレーター',
      additional: [
        'スマートフォーム自動入力',
        '自動データ入力',
        'AIフォームアシスタント',
        'インテリジェントフォーム入力',
        'Chromeフォーム自動入力',
        'ワンクリックフォーム入力'
      ]
    }
  },
  privacy: {
    meta: {
      title: 'プライバシーポリシー - Fillify',
      description: 'Fillifyがプライバシーをどのように保護し、データを管理しているかをご確認ください。'
    },
    title: 'プライバシーポリシー',
    lastUpdated: '最終更新日: {date}'
  },
  terms: {
    meta: {
      title: '利用規約 - Fillify',
      description: 'Fillifyのサービス利用に関する規約をご確認ください。'
    },
    title: '利用規約',
    lastUpdated: '最終更新日: {date}'
  },
  dashboard: {
    meta: {
      title: 'ダッシュボード - Fillify',
      description: 'Fillifyアカウントの管理、現在のプランの確認、使用状況の追跡ができます。'
    },
    currentPlan: '現在のプラン',
    settings: '設定',
    usageOverview: '使用状況の概要',
    creditsUsed: '使用済みクレジット'
  },
  blog: {
    meta: {
      title: 'ブログ - Fillify',
      description: 'AIによるフォーム入力と生産性自動化に関する最新ニュース、アップデート、ヒントをお読みください。'
    },
    hero: {
      badge: '最新情報',
      title: 'ブログ',
      subtitle: '最新ニュース、リリース、ヒント'
    },
    list: {
      readMore: '続きを読む',
      publishedOn: '公開日',
      minRead: '分で読める',
      noPostsTitle: 'まだ投稿がありません',
      noPostsDescription: '素晴らしいコンテンツを作成中です。しばらくしてからまた確認してください。'
    },
    article: {
      backToBlog: 'ブログに戻る',
      thanksTitle: 'お読みいただきありがとうございます！',
      thanksDescription: 'Fillifyについてご質問やご提案がございましたら、お気軽にお問い合わせください。',
      tryFillify: 'Fillifyを試す',
      moreArticles: 'その他の記事',
      notFoundTitle: '記事が見つかりません',
      notFoundDescription: '申し訳ございませんが、お探しの記事は存在しないか削除されています。',
      backToBlogBtn: 'ブログに戻る'
    }
  },
  '404': {
    title: 'ページが見つかりません',
    description: '申し訳ありませんが、お探しのページが見つかりません。URLをご確認いただくか、ホームページにお戻りください。',
    backHome: 'ホームに戻る'
  },
  demo: {
    meta: {
      title: 'デモフォーム - Fillifyのオートフィル機能を試す',
      description: 'このインタラクティブなデモフォームでFillifyのAI駆動のフォーム入力機能を試してください。AIがフォーム入力体験をどのように変えるかご覧ください。'
    }
  },
  success: {
    meta: {
      title: '注文成功 - Fillify',
      description: 'ご注文ありがとうございます！Fillifyのサブスクリプションが有効になりました。'
    }
  },
  cancel: {
    meta: {
      title: '注文キャンセル - Fillify',
      description: 'ご注文がキャンセルされました。ご利用いただきありがとうございました。'
    }
  },
  refund: {
    meta: {
      title: '返金ポリシー - Fillify',
      description: 'Fillifyの返金ポリシーおよびサブスクリプションの返金申請方法についてご覧ください。'
    }
  },
  auth: {
    callback: {
      processing: '認証処理中...'
    }
  },
  faqPage: {
    meta: {
      title: 'よくある質問 - Fillify',
      description: 'フォーム自動入力、メール作成、バグレポート生成を行うAI搭載ツール「Fillify」に関するよくある質問の回答をご覧ください。'
    },
    title: 'よくある質問',
    subtitle: 'FillifyのAI搭載フォーム入力、メール作成、バグレポート生成ツールに関するよくある質問にお答えします。',
    sections: {
      what: {
        title: 'Fillifyとは何ですか？',
        content: 'Fillifyは、ウェブフォーム、メール、バグレポートとのやり取りを変革するAI搭載ブラウザ拡張機能です。インテリジェントなソリューションにより、最小限のユーザー入力でフォームの自動入力、メール作成、バグレポート生成が可能となり、手動作業に費やす時間を何時間も節約できます。高度なAI技術を使用することで、Fillifyはコンテキストを理解し、フィールドを正確かつ効率的に入力します。'
      },
      types: {
        title: 'Fillifyで入力できるフォームの種類は？',
        content: 'Fillifyは、連絡・照会フォーム、求人応募フォーム、登録・サインアップフォーム、アンケート・調査フォーム、eコマースチェックアウトフォーム、サポートチケット、フィードバック・レビュー、カスタムビジネスフォームなど、さまざまな種類のフォームに対応しています。当社のAI技術は、異なるフォーム構造やフィールドタイプに適応し、正確で効率的なフォーム入力を提供します。'
      },
      providers: {
        title: 'FillifyはどのAIプロバイダーを使用しますか？',
        content: 'Fillifyは、柔軟性と選択肢を提供するため、複数のAIプロバイダーをサポートしています：OpenAIのGPTモデル、AnthropicのClaudeモデル、GoogleのGeminiモデル、Mistral AIモデル、およびカスタムAIモデル。ニーズ、プライバシー設定、パフォーマンス要件に応じて、さまざまなAIプロバイダーを切り替えることができます。各プロバイダーは異なるユースケースに特化した独自の強みを提供します。'
      },
      privacy: {
        title: 'Fillifyはプライバシーとセキュリティをどのように保証しますか？',
        content: 'プライバシーとセキュリティはFillifyの最優先事項です：サーバーに個人データを保存することはありません、すべてのフォームデータ処理はブラウザ内でローカルで行われ、AIプロバイダーとの通信は暗号化されています、GDPRおよびその他のプライバシー規制を遵守しています、AIプロバイダーと共有される情報の完全な管理権を保有しています、そしてフォーム入力に必要な最小限のデータのみを使用します。機密情報は常にご自身の管理下にあります。'
      },
      customize: {
        title: 'Fillifyの動作をカスタマイズできますか？',
        content: 'もちろんです！Fillifyは詳細なカスタマイズオプションを提供します：頻繁に使用する情報のカスタムテンプレート作成、AI応答形式・スタイルの設定、データを含む個人プロファイルの設定、データ共有のプライバシー設定の調整、タスク別に特定のAIプロバイダーの選択、フォームフィールドマッピングの設定など。これらのカスタマイズにより、Fillifyは完全に必要に応じて動作できるようになります。'
      },
      languages: {
        title: 'Fillifyがサポートする言語は？',
        content: 'Fillifyはインターフェースとコンテンツ処理の両方で多言語をサポートしています：英語、スペイン語、フランス語、ドイツ語、日本語、韓国語、中国語（簡体字・繁体字）、ロシア語。AIモデルはこれらの言語でコンテンツを理解および生成できるため、国際ユーザーおよび多言語フォームに適しています。'
      },
      gettingStarted: {
        title: 'Fillifyの使用を始めるには？',
        content: 'Fillifyの使用開始は簡単です：Chrome拡張機能ストアまたはMicrosoft Edgeアドオンから拡張機能をインストールし、無料アカウントを作成または既存アカウントにサインインし、AIプロバイダーの設定を構成し、頻繁に使用する情報で個人プロファイルを設定し、あらゆるウェブフォーム、メール、バグレポートでFillifyを使用し始めます。数分以内にAI駆動の自動化で時間を節約できます！'
      },
      accuracy: {
        title: 'FillifyのAIの精度はどのくらいですか？',
        content: 'FillifyのAI精度は、コンテキスト、セマンティクス、特定のフォーム要件を理解する高度な機械学習アルゴリズムにより一貫して高水準を維持しています。当社のシステムは、入力データのパターンを学習し、異なるフォームタイプとフィールドに適応することで高精度を実現しています。ユーザーのフィードバックや自然言語処理技術の新進展に基づいてAIモデルを継続的に改善しています。'
      }
    },
    cta: {
      title: 'それでも質問がありますか？',
      description: '質問の答えが見つからない場合は、直接お気軽にお問い合わせください。',
      button: 'お問い合わせ'
    }
  },
  howItWorks: {
    meta: {
      title: 'Fillifyの仕組み - AIフォーム入力ガイド',
      description: 'FillifyのAI搭載ブラウザ拡張機能がフォームの自動入力、メール作成、バグレポート生成を行う仕組みをご覧ください。毎週数時間節約するシンプルな6ステッププロセス。'
    },
    title: 'Fillifyの仕組み',
    subtitle: 'わずか数ステップでオンラインフォーム入力体験を変えるAI搭載ブラウザ拡張機能の仕組みをご覧ください。',
    steps: {
      install: {
        title: '拡張機能をインストール',
        description: 'Chrome拡張機能ストアまたはMicrosoft EdgeアドオンからFillifyをダウンロード・インストールします。拡張機能はブラウザとシームレスに統合されます。',
        features: [
          'ワンクリックでのインストール',
          '追加ソフトウェア不要',
          '既存ブラウザで動作'
        ]
      },
      configure: {
        title: 'AIプロバイダーを設定',
        description: 'APIキーを追加して、お好みのAIプロバイダー（OpenAI、Claude、Geminiなど）を接続します。データの完全管理が可能です。',
        features: [
          '複数AIプロバイダー対応',
          '安全なAPIキー保管',
          'プロバイダー間での簡単切り替え'
        ]
      },
      describe: {
        title: '必要事項を記述',
        description: 'フォーム、メール、バグレポートに出会った際、自然言語で入力したい内容を記述してください。FillifyのAIは指示を理解します。',
        features: [
          '自然言語処理',
          'コンテキスト認識による記入',
          'あらゆるフォームタイプに対応'
        ]
      },
      aiFill: {
        title: 'AIが自動的に記入',
        description: 'Fillifyはフォーム構造を分析し、記述に基づいてフィールドをインテリジェントに自動入力します。送信前に入力内容を確認・編集できます。',
        features: [
          'スマートフィールド検出',
          'コンテキスト理解',
          'リアルタイム記入完了'
        ]
      },
      review: {
        title: '確認して送信',
        description: '入力された情報を確認し、必要な調整を行い、作業が迅速かつ正確に完了したと確認した上で送信します。',
        features: [
          '人間による確認ステップ',
          '簡単な編集機能',
          '安全な送信'
        ]
      },
      saveTime: {
        title: '時間節約と集中',
        description: '重要なタスクに時間を費やす自由を得て、Fillifyが繰り返しのフォーム入力作業を処理する体験を。',
        features: [
          '週に何時間も節約',
          '重要な作業に集中',
          '繰り返し作業の削減'
        ]
      }
    },
    technology: {
      title: 'Fillifyの裏側の技術',
      sections: {
        aiAnalysis: {
          title: 'AI搭載分析',
          description: 'Fillifyは、フォーム構造、フィールドタイプ、異なる入力間の関係を理解するための高度な機械学習モデルを使用しています。これにより、複雑なフォームでも正確な入力が可能になります。'
        },
        nlp: {
          title: '自然言語処理',
          description: 'シンプルな日本語で希望内容を記述すると、FillifyのNLPエンジンが指示を正確なフォーム記入に変換し、コンテキストと要件を理解します。'
        },
        privacy: {
          title: 'プライバシー重視の設計',
          description: 'すべての処理はブラウザ内で行われ、データはサーバーに送信されることはありません。フォーム入力にAIを使用する際も、情報はプライベートかつ安全に保たれます。'
        },
        adaptive: {
          title: '適応的学習',
          description: 'Fillifyは使用パターンと設定から学習し、時間とともに特定のニーズに応じたフォームの入力をより正確かつ効率的に行えるようになります。'
        }
      }
    },
    cta: {
      title: 'フォーム入力体験を変える準備はできましたか？',
      description: '毎週何時間も節約している数千人のユーザーに加わって、FillifyのAI駆動フォーム入力をお試しください。',
      button: '無料で始める'
    }
  }
}