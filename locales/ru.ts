export default {
  $locale: {
    name: 'Russian',
    nativeName: 'Русский'
  },
  welcome: 'Добро пожаловать в Fillify',
  description: 'Заполнение форм с использованием ИИ',
  nav: {
    home: 'Главная',
    blog: 'Блог',
    signin: 'Войти',
    dashboard: 'Панель управления',
    signout: 'Выйти',
    startFree: 'Начать бесплатно',
    language: 'Язык'
  },
  hero: {
    chromeStore: 'Теперь доступно в Chrome Web Store',
    edgeStore: 'Теперь доступно в надстройках Microsoft Edge',
    title: {
      text: 'Заполняйте формы за считанные секунды с помощью ',
      staticWord: 'интеллектуального ИИ',
      rotatingWords: [
        'магии на основе ИИ',
        'умной автоматизации',
        'технологий будущего',
        'идеальной точности',
        'бесшовной интеграции'
      ]
    },
    description: 'Просто напишите одно предложение, и ИИ мгновенно заполнит любую веб-форму. Умнейший способ обработки онлайн-форм.',
    cta: {
      chrome: 'Добавить в Chrome',
      edge: 'Добавить в Edge',
      learnMore: 'Узнать больше'
    }
  },
  stats: {
    forms: {
      value: '+',
      label: 'Формы, заполняемые ежедневно'
    },
    accuracy: {
      value: '%',
      label: 'Точность'
    },
    support: {
      value: '24/7',
      label: 'Поддержка ИИ'
    }
  },
  features: {
    title: 'Испытайте мощь ИИ',
    subtitle: 'Узнайте, как Fillify трансформирует ваш ежедневный рабочий процесс с помощью интеллектуальной автоматизации',
    formFilling: {
      title: 'Умное распознавание форм',
      description: 'Fillify мгновенно распознает и анализирует любую веб-форму на странице. Наш продвинутый ИИ сканирует поля формы, понимает их назначение и готовит интеллектуальное автозаполнение. От простых контактных форм до сложных многоэтапных заявок — Fillify распознает всё.',
      alt: 'Скриншот, показывающий автоматическое обнаружение и выделение полей формы на веб-странице в Fillify',
      demoTitle: 'Умное распознавание форм',
      fieldsDetected: 'полей обнаружено'
    },
    email: {
      title: 'Умный и Пользовательский режимы',
      description: 'Оцените нашу автоматизацию на базе ИИ с умным режимом для интеллектуального автоматического обнаружения или переключитесь на пользовательский режим для специализированного заполнения форм, написания электронных писем или создания отчетов об ошибках. Выберите подход, который лучше всего подходит для вашей задачи.',
      alt: 'Интерфейс, отображающий умный и пользовательский режимы с соответствующими опциями',
      demoTitle: 'Ознакомьтесь с режимами',
      promptLabel: 'Опишите, что вам нужно',
      promptPlaceholder: 'Введите ваш запрос здесь...',
      generate: 'Генерировать',
      generating: 'Генерация...',
      label: 'Режим',
      promptExample: 'Заполните эту форму заявки на работу с моей информацией из резюме, включая опыт работы в Tech Corp.',
      emailPromptExample: 'Напишите профессиональное письмо-подтверждение клиенту о сроке проекта и результатах.',
      bugPromptExample: 'Создайте отчет об ошибке для ошибки входа в систему: пользователи не могут войти в систему после сброса пароля.'
    },
    bugReport: {
      title: 'Магия мгновенного автозаполнения',
      description: 'Наблюдайте, как ИИ превращает ваше простое описание в идеально отформатированный контент и автоматически заполняет каждое поле формы. Больше никакого утомительного набора текста или копирования-вставки. Просто опишите, что вам нужно, на русском языке, и Fillify справится с остальным с молниеносной скоростью и идеальной точностью.',
      alt: 'Анимация, показывающая автоматическое заполнение полей формы контентом, сгенерированным ИИ, в реальном времени',
      demoTitle: 'Автозаполнение ИИ в действии',
      filling: 'ИИ заполняет форму...',
      completed: 'Форма успешно заполнена'
    },
    aiProvider: {
      title: 'Ваш ИИ, ваш выбор, ваша безопасность',
      description: 'Полная свобода выбора предпочитаемого поставщика ИИ. Поддержка OpenAI, Claude, Gemini и локальных моделей через Ollama. Ваши API-ключи остаются в безопасности в вашем браузере, а поддержка локальных моделей означает, что ваши конфиденциальные данные никогда не покидают ваше устройство. Дизайн, ориентированный на приватность, с корпоративным уровнем безопасности.',
      alt: 'Безопасный интерфейс, показывающий множественные опции поставщиков ИИ с акцентом на локальные модели и защиту приватности',
      demoTitle: 'Множественные поставщики ИИ',
      secureLocal: 'Поддержка локальных моделей',
      privacyFirst: 'Приватность прежде всего',
      localModelDesc: 'Подключайтесь к Ollama для локальных моделей ИИ - ваши данные никогда не покидают устройство'
    }
  },
  faq: {
    title: 'Часто задаваемые вопросы',
    items: {
      what: {
        question: 'Что такое Fillify?',
        answer: 'Fillify — это расширение для браузера, работающее на основе ИИ, которое помогает заполнять формы, составлять письма и создавать подробные отчеты об ошибках в одно нажатие, используя передовые ИИ-модели для понимания ваших потребностей и автоматического заполнения полей, экономя ваше время и усилия на повторяющихся задачах.'
      },
      types: {
        question: 'Какие типы форм может обрабатывать Fillify?',
        answer: 'Fillify работает с различными формами, включая общие веб-формы, отчеты об ошибках и электронные письма. Он поддерживает текстовые поля, текстовые области и другие элементы, обеспечивая бесшовную автоматизацию на различных веб-сайтах.'
      },
      providers: {
        question: 'Каких поставщиков ИИ поддерживает Fillify?',
        answer: 'Fillify поддерживает несколько ведущих поставщиков ИИ, таких как GPT-модели от OpenAI, серия Claude от Anthropic, модели Gemini от Google, DeepSeek, Moonshot и локально запускаемые ИИ-модели через интеграцию с Ollama, обеспечивая максимальную гибкость и выбор в том, как генерируется контент.'
      },
      privacy: {
        question: 'Как Fillify защищает мои данные и конфиденциальность?',
        answer: "Fillify никогда не отправляет ваши API-ключи или данные форм на наши серверы - ваши ключи хранятся в безопасности в вашем браузере, и все запросы идут напрямую с вашего устройства к поставщику ИИ. Это означает, что только вы и выбранный вами поставщик когда-либо увидят ваши данные. Для еще более надежного контроля Fillify поддерживает интеграцию с Ollama, чтобы вы могли подключаться к локально запускаемым ИИ-моделям, обеспечивая максимальную безопасность и конфиденциальность."
      },
      customize: {
        question: 'Могу ли я настраивать ответы ИИ для определенных форм?',
        answer: 'Да! В режиме отчетов об ошибках вы можете создавать собственные шаблоны с заранее определенной информацией, чтобы помогать генерировать более точные и последовательные отчеты об ошибках.'
      },
      languages: {
        question: 'Какие языки поддерживает Fillify?',
        answer: 'Fillify поддерживает несколько языков и может автоматически определять язык формы. Вы также можете вручную выбрать предпочитаемый язык вывода во всплывающем окне расширения.'
      }
    }
  },
  bottomCta: {
    subtitle: 'Готовы изменить свой рабочий процесс?',
    title: 'Испытайте будущее заполнения форм уже сегодня',
    button: 'Установить сейчас'
  },
  footer: {
    copyright: '© {year} Fillify. Все права защищены.',
    social: {
      twitter: 'X (Twitter)',
      youtube: 'YouTube'
    },
    links: {
      terms: 'Условия обслуживания',
      privacy: 'Политика конфиденциальности'
    }
  },
  signin: {
    title: 'Добро пожаловать в Fillify',
    subtitle: 'Войдите, чтобы использовать наше расширение для заполнения форм на базе ИИ',
    email: {
      label: 'Адрес электронной почты',
      placeholder: 'Введите адрес электронной почты',
      continue: 'Продолжить с электронной почтой',
      sending: 'Отправка...'
    },
    otp: {
      sentMessage: 'Мы отправили код подтверждения на',
      label: 'Код входа',
      placeholder: 'Введите код подтверждения',
      continue: 'Продолжить с кодом входа',
      verifying: 'Проверка...',
      resend: {
        resending: 'Повторная отправка...',
        cooldown: 'Повторная отправка через {seconds}с',
        action: 'Повторно отправить код'
      }
    },
    google: {
      continue: 'Продолжить с Google',
      loading: 'Перенаправление...'
    },
    divider: 'или',
    error: {
      invalidEmail: 'Пожалуйста, введите действительный адрес электронной почты',
      sendOTP: 'Не удалось отправить код подтверждения. Пожалуйста, попробуйте снова.',
      invalidOTP: 'Пожалуйста, введите действительный 6-значный код подтверждения',
      verifyOTP: 'Неверный или просроченный код подтверждения. Пожалуйста, попробуйте снова.',
      googleLogin: 'Ошибка входа через Google. Пожалуйста, попробуйте снова.'
    },
    features: {
      title: 'Что вы получите:',
      list: {
        autoFill: 'Автозаполнение форм с использованием ИИ',
        api: 'Настройка с вашим собственным API',
        early: 'Ранний доступ к новым функциям'
      }
    },
    terms: {
      prefix: 'Войдя, вы соглашаетесь с нашими',
      and: 'и',
      termsOfService: 'Условия обслуживания',
      privacyPolicy: 'Политика конфиденциальности'
    },
    seo: {
      title: 'Войти - Fillify',
      description: 'Войдите в Fillify, чтобы получить доступ к функциям автозаполнения форм на основе ИИ'
    }
  },
  meta: {
    title: 'Fillify – Автоматический заполнитель форм на базе ИИ',
    description: 'Fillify - это автоматический заполнитель форм на базе ИИ, который мгновенно заполняет любую онлайн-форму. Экономьте время с автозаполнением в один клик на основе ИИ.',
    keywords: {
      formFilling: 'Автозаполнитель форм на базе ИИ',
      automation: 'Автоматическое заполнение форм',
      email: 'Помощник по электронной почте на базе ИИ',
      bugReport: 'Генератор отчетов об ошибках на базе ИИ',
      additional: [
        'Умное автозаполнение форм',
        'Автоматический ввод данных',
        'Помощник по заполнению форм на базе ИИ',
        'Интеллектуальное заполнение форм',
        'Автозаполнение форм Chrome',
        'Автозаполнитель форм в один клик'
      ]
    }
  },
  privacy: {
    meta: {
      title: 'Политика конфиденциальности - Fillify',
      description: 'Узнайте, как Fillify защищает вашу конфиденциальность и обрабатывает ваши данные.'
    },
    title: 'Политика конфиденциальности',
    lastUpdated: 'Последнее обновление: {date}'
  },
  terms: {
    meta: {
      title: 'Условия обслуживания - Fillify',
      description: 'Прочитайте об условиях и правилах использования услуг Fillify.'
    },
    title: 'Условия обслуживания',
    lastUpdated: 'Последнее обновление: {date}'
  },
  dashboard: {
    meta: {
      title: 'Панель управления - Fillify',
      description: 'Управляйте своим аккаунтом Fillify, просматривайте текущий план и отслеживайте использование.'
    },
    currentPlan: 'Текущий план',
    settings: 'Настройки',
    usageOverview: 'Обзор использования',
    creditsUsed: 'Использованные кредиты'
  },
  blog: {
    meta: {
      title: 'Блог - Fillify',
      description: 'Читайте последние новости, обновления и советы о заполнении форм с помощью ИИ и автоматизации продуктивности.'
    },
    hero: {
      badge: 'Последние обновления',
      title: 'Блог',
      subtitle: 'Последние новости, релизы и советы'
    },
    list: {
      readMore: 'Читать дальше',
      publishedOn: 'Опубликовано',
      minRead: 'мин чтения',
      noPostsTitle: 'Пока никаких постов',
      noPostsDescription: 'Мы работаем над созданием отличного контента. Пожалуйста, проверьте позже.'
    },
    article: {
      backToBlog: 'Назад к блогу',
      thanksTitle: 'Спасибо за чтение!',
      thanksDescription: 'Если у вас есть вопросы или предложения о Fillify, не стесняйтесь обращаться к нам.',
      tryFillify: 'Попробовать Fillify',
      moreArticles: 'Больше статей',
      notFoundTitle: 'Статья не найдена',
      notFoundDescription: 'Извините, статья, которую вы ищете, не существует или была удалена.',
      backToBlogBtn: 'Назад к блогу'
    }
  },
  '404': {
    title: 'Страница не найдена',
    description: 'Извините, мы не смогли найти страницу, которую вы ищете. Пожалуйста, проверьте URL или вернитесь на главную страницу.',
    backHome: 'Вернуться на главную'
  },
  demo: {
    meta: {
      title: 'Демонстрационная форма - Проверьте автозаполнение Fillify',
      description: 'Протестируйте возможности автозаполнения на основе ИИ с помощью этой интерактивной демонстрационной формы. Посмотрите, как ИИ может изменить ваш опыт заполнения форм.'
    }
  },
  success: {
    meta: {
      title: 'Заказ успешно оформлен - Fillify',
      description: 'Спасибо за ваш заказ! Ваша подписка Fillify теперь активна.'
    }
  },
  cancel: {
    meta: {
      title: 'Заказ отменен - Fillify',
      description: 'Ваш заказ был отменен. Нам жаль, что вы уходите.'
    }
  },
  refund: {
    meta: {
      title: 'Политика возврата - Fillify',
      description: 'Узнайте о политике возврата Fillify и о том, как запросить возврат для своей подписки.'
    }
  },
  auth: {
    callback: {
      processing: 'Обработка аутентификации...'
    }
  },
  faqPage: {
    meta: {
      title: 'Часто задаваемые вопросы - Fillify',
      description: 'Найдите ответы на общие вопросы о Fillify, инструменте на основе ИИ, который автоматически заполняет формы, составляет электронные письма и генерирует отчеты об ошибках.'
    },
    title: 'Часто задаваемые вопросы',
    subtitle: 'Получите ответы на общие вопросы об инструментах заполнения форм, составления электронных писем и генерации отчетов об ошибках на основе ИИ от Fillify.',
    sections: {
      what: {
        title: 'Что такое Fillify?',
        content: 'Fillify - это расширение для браузера на основе ИИ, которое революционизирует взаимодействие с веб-формами, электронной почтой и отчетами об ошибках. Наше интеллектуальное решение автоматически заполняет формы, составляет электронные письма и генерирует отчеты об ошибках с минимальным вводом данных пользователем, экономя часы ручной работы. Используя передовые технологии ИИ, Fillify понимает контекст и точно и эффективно заполняет поля.'
      },
      types: {
        title: 'Какие типы форм может заполнять Fillify?',
        content: 'Fillify предназначен для обработки широкого спектра типов форм, включая формы контактов и запросов, формы заявок на работу, формы регистрации и регистрации, формы опросов и анкет, формы оформления заказов в электронной коммерции, формы тикетов поддержки, формы отзывов и обзоров, а также настраиваемые бизнес-формы. Наша технология ИИ адаптируется к различным структурам форм и типам полей для обеспечения точного и эффективного заполнения форм.'
      },
      providers: {
        title: 'Каких поставщиков ИИ использует Fillify?',
        content: 'Fillify поддерживает несколько поставщиков ИИ, чтобы предоставить вам гибкость и выбор: GPT-модели от OpenAI, модели Claude от Anthropic, модели Gemini от Google, модели Mistral AI и настраиваемые модели ИИ. Вы можете переключаться между различными поставщиками ИИ в зависимости от ваших потребностей, предпочтений конфиденциальности и требований к производительности. Каждый поставщик предлагает уникальные преимущества для различных случаев использования.'
      },
      privacy: {
        title: 'Как Fillify обеспечивает конфиденциальность и безопасность?',
        content: 'Конфиденциальность и безопасность являются нашим главным приоритетом в Fillify: мы не храним ваши личные данные на наших серверах, вся обработка данных форм происходит локально в вашем браузере, связь с поставщиками ИИ зашифрована, мы соблюдаем GDPR и другие нормы конфиденциальности, вы полностью контролируете информацию, которой делитесь с поставщиками ИИ, и мы используем минимальное количество данных, необходимых для заполнения форм. Ваши конфиденциальные данные всегда остаются вашими.'
      },
      customize: {
        title: 'Могу ли я настроить поведение Fillify?',
        content: 'Абсолютно! Fillify предлагает обширные возможности настройки: создавайте настраиваемые шаблоны для часто используемой информации, настраивайте форматы и стили ответов ИИ, создавайте личные профили с вашими данными, корректируйте настройки конфиденциальности для обмена данными, выбирайте конкретных поставщиков ИИ для различных задач и настраивайте предпочтения сопоставления полей форм. Эти настройки позволяют Fillify работать именно так, как вам нужно.'
      },
      languages: {
        title: 'Какие языки поддерживает Fillify?',
        content: 'Fillify поддерживает несколько языков для интерфейса и обработки контента: английский, испанский, французский, немецкий, японский, корейский, китайский (упрощенный и традиционный) и русский. Модели ИИ могут понимать и генерировать контент на этих языках, что делает Fillify полезным для международных пользователей и многоязычных форм.'
      },
      gettingStarted: {
        title: 'Как начать работу с Fillify?',
        content: 'Начать работу с Fillify просто: установите расширение из Chrome Web Store или надстроек Microsoft Edge, создайте бесплатную учетную запись или войдите, если у вас уже есть учетная запись, настройте свои предпочтения поставщика ИИ, настройте свой личный профиль с информацией, которую вы часто используете, и начните использовать Fillify на любой веб-форме, электронном письме или отчете об ошибке. Вы будете экономить время с помощью автоматизации на основе ИИ всего за несколько минут!'
      },
      accuracy: {
        title: 'Насколько точен ИИ в Fillify?',
        content: 'Точность ИИ в Fillify постоянно высока благодаря нашим передовым алгоритмам машинного обучения, которые понимают контекст, семантику и конкретные требования форм. Наша система достигает высокой точности, обучаясь на шаблонах в ваших входных данных и адаптируясь к различным типам и полям форм. Мы постоянно совершенствуем наши модели ИИ на основе отзывов пользователей и новых достижений в технологиях обработки естественного языка.'
      }
    },
    cta: {
      title: 'Остались вопросы?',
      description: 'Если вы не можете найти ответ на свой вопрос, не стесняйтесь связаться с нами напрямую.',
      button: 'Связаться с нами'
    }
  },
  howItWorks: {
    meta: {
      title: 'Как работает Fillify - Руководство по заполнению форм с помощью ИИ',
      description: 'Узнайте, как работает расширение для браузера на основе ИИ от Fillify, чтобы автоматически заполнять формы, составлять электронные письма и генерировать отчеты об ошибках. Простой 6-ступенчатый процесс для экономии часов каждый день.'
    },
    title: 'Как работает Fillify',
    subtitle: 'Узнайте, как наше расширение для браузера на основе ИИ преобразует ваш онлайн-опыт заполнения форм всего за несколько простых шагов.',
    steps: {
      install: {
        title: 'Установите расширение',
        description: 'Загрузите и установите Fillify из Chrome Web Store или надстроек Microsoft Edge. Расширение без проблем интегрируется с вашим браузером.',
        features: [
          'Установка в один клик',
          'Не требуется дополнительное программное обеспечение',
          'Работает с существующим браузером'
        ]
      },
      configure: {
        title: 'Настройте поставщика ИИ',
        description: 'Подключите своего предпочтительного поставщика ИИ (OpenAI, Claude, Gemini и т.д.), добавив свой API-ключ. Вы сохраняете полный контроль над своими данными.',
        features: [
          'Поддержка нескольких поставщиков ИИ',
          'Безопасное хранение API-ключей',
          'Легкое переключение между поставщиками'
        ]
      },
      describe: {
        title: 'Опишите свои потребности',
        description: 'Когда вы сталкиваетесь с формой, электронным письмом или отчетом об ошибке, опишите, что вы хотите заполнить, на естественном языке. ИИ Fillify понимает ваши инструкции.',
        features: [
          'Обработка естественного языка',
          'Заполнение с учетом контекста',
          'Адаптируется к любому типу формы'
        ]
      },
      aiFill: {
        title: 'ИИ заполняет автоматически',
        description: 'Fillify анализирует структуру формы и интеллектуально заполняет поля на основе вашего описания. Вы можете просмотреть и отредактировать перед отправкой.',
        features: [
          'Интеллектуальное обнаружение полей',
          'Понимание контекста',
          'Заполнение в реальном времени'
        ]
      },
      review: {
        title: 'Просмотрите и отправьте',
        description: 'Проверьте заполненную информацию, внесите необходимые корректировки и отправьте с уверенностью в том, что работа была выполнена быстро и точно.',
        features: [
          'Шаг проверки человеком',
          'Легкие возможности редактирования',
          'Безопасная отправка'
        ]
      },
      saveTime: {
        title: 'Экономьте время и сосредоточьтесь',
        description: 'Почувствуйте свободу тратить время на важные задачи, пока Fillify справляется с повторяющимися задачами заполнения форм.',
        features: [
          'Экономьте часы каждую неделю',
          'Сосредоточьтесь на важной работе',
          'Уменьшите повторяющиеся задачи'
        ]
      }
    },
    technology: {
      title: 'Технология за Fillify',
      sections: {
        aiAnalysis: {
          title: 'Анализ на основе ИИ',
          description: 'Fillify использует передовые модели машинного обучения для понимания структур форм, типов полей и взаимосвязей между различными входными данными. Это позволяет точно заполнять даже сложные формы.'
        },
        nlp: {
          title: 'Обработка естественного языка',
          description: 'Опишите, что вы хотите, на простом русском языке, и движок NLP Fillify преобразует ваши инструкции в точные заполнения форм, понимая контекст и требования.'
        },
        privacy: {
          title: 'Дизайн с приоритетом конфиденциальности',
          description: 'Вся обработка происходит в вашем браузере, и ваши данные никогда не касаются наших серверов. Ваша информация остается конфиденциальной и безопасной при использовании ИИ для заполнения форм.'
        },
        adaptive: {
          title: 'Адаптивное обучение',
          description: 'Fillify обучается на ваших моделях использования и предпочтениях, становясь все более точным и эффективным при заполнении форм в соответствии с вашими конкретными потребностями со временем.'
        }
      }
    },
    cta: {
      title: 'Готовы преобразовать свой опыт заполнения форм?',
      description: 'Присоединяйтесь к тысячам пользователей, которые экономят часы каждую неделю с помощью заполнения форм на основе ИИ от Fillify.',
      button: 'Начать бесплатно'
    }
  }
}