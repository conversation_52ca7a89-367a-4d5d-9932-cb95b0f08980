<template>
  <div class="min-h-screen flex items-center justify-center">
    <div class="text-center">
      <div v-if="!error" class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
      <div v-else class="mx-auto h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </div>
      
      <p v-if="!error" class="mt-4 text-lg text-gray-700">{{ $t('auth.callback.processing') }}</p>
      <div v-else class="mt-4">
        <p class="text-lg font-medium text-red-600">{{ $t('auth.callback.error') }}</p>
        <p class="mt-2 text-sm text-gray-600">{{ error }}</p>
        <div class="mt-4">
          <button 
            @click="retryAuth"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 mr-3"
          >
            {{ $t('auth.callback.retry') }}
          </button>
          <NuxtLink 
            :to="localePath('/signin')"
            class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            {{ $t('auth.callback.backToSignin') }}
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useAuth } from '~/composables/useAuth'
import { useSupabase } from '~/composables/useSupabase'

const { fetchUserData } = useAuth()
const { locale, t } = useI18n()
const localePath = useLocalePath()
const supabase = useSupabase()
const error = ref('')

// Handle the callback when the component is mounted
const handleCallback = async () => {
  try {
    if (!supabase) {
      throw new Error('Supabase client not initialized')
    }

    // Handle the OAuth callback
    const { data, error: authError } = await supabase.auth.getSession()
    
    if (authError) {
      console.error('Error getting session:', authError)
      throw authError
    }

    if (data.session?.user) {
      // Session exists, fetch user data to ensure everything is synced
      await fetchUserData()
      
      // Redirect to dashboard
      const currentLang = locale.value
      const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
      navigateTo(dashboardPath, { replace: true })
    } else {
      // No session found, redirect to sign in
      throw new Error('No session found')
    }
  } catch (err: any) {
    console.error('Error processing auth callback:', err)
    error.value = err.message || 'Authentication failed'
  }
}

const retryAuth = () => {
  error.value = ''
  handleCallback()
}

onMounted(() => {
  handleCallback()
})

// SEO
useHead({
  title: 'Authentication Callback'
})
</script>