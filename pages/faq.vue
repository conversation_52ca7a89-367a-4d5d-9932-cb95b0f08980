<template>
  <div class="min-h-screen relative">
    <main class="relative">
      <!-- Hero Section -->
      <section class="container mx-auto px-4 pt-32 pb-16 md:pt-40 md:pb-20 relative">
        <!-- Gradient Accent -->
        <div class="absolute top-0 left-1/2 -translate-x-1/2 h-[400px] w-[600px] bg-blue-500/10 rounded-full blur-[100px] opacity-30 -z-[1]"></div>

        <div class="max-w-4xl mx-auto text-center space-y-6 relative">
          <!-- Badge -->
          <div class="inline-block animate-slide-up">
            <div class="flex items-center gap-2 px-4 py-2 text-sm rounded-full border border-blue-200 bg-blue-50/80 text-blue-600 font-medium backdrop-blur-sm">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-help-circle">
                <circle cx="12" cy="12" r="10"/>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
                <path d="M12 17h.01"/>
              </svg>
              FAQ
            </div>
          </div>

          <!-- Main Heading -->
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-gray-900 animate-slide-up [animation-delay:200ms]">
            <span class="bg-gradient-to-r from-blue-600 to-indigo-600 text-transparent bg-clip-text">{{ t('faqPage.title') }}</span>
          </h1>

          <!-- Description -->
          <p class="text-xl md:text-2xl text-gray-600 max-w-2xl mx-auto leading-relaxed animate-slide-up [animation-delay:400ms]">
            {{ t('faqPage.subtitle') }}
          </p>
        </div>
      </section>

      <!-- FAQ Content Section -->
      <section class="container mx-auto px-4 pb-16 md:pb-20 relative">
        <div class="max-w-4xl mx-auto space-y-4">
          <!-- FAQ Item 1: What is Fillify -->
          <div
            class="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 hover:border-blue-300/60 transition-all duration-300 shadow-lg hover:shadow-xl animate-slide-up overflow-hidden"
            style="animation-delay: 600ms"
          >
            <button
              @click="toggleFaq(0)"
              class="w-full flex items-center justify-between p-6 md:p-8 text-left group"
            >
              <h2 class="text-xl md:text-2xl font-bold text-gray-900 pr-4 group-hover:text-blue-600 transition-colors">
                {{ t('faqPage.sections.what.title') }}
              </h2>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="flex-shrink-0 transition-transform duration-300"
                :class="openFaq === 0 ? 'rotate-180' : ''"
              >
                <polyline points="6 9 12 15 18 9"/>
              </svg>
            </button>
            <div
              class="overflow-hidden transition-all duration-300"
              :class="openFaq === 0 ? 'max-h-96' : 'max-h-0'"
            >
              <div class="px-6 md:px-8 pb-6 md:pb-8">
                <p class="text-lg text-gray-700 leading-relaxed">
                  {{ t('faqPage.sections.what.content') }}
                </p>
              </div>
            </div>
          </div>

          <!-- FAQ Item 2: Form Types -->
          <div
            class="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 hover:border-blue-300/60 transition-all duration-300 shadow-lg hover:shadow-xl animate-slide-up overflow-hidden"
            style="animation-delay: 700ms"
          >
            <button
              @click="toggleFaq(1)"
              class="w-full flex items-center justify-between p-6 md:p-8 text-left group"
            >
              <h2 class="text-xl md:text-2xl font-bold text-gray-900 pr-4 group-hover:text-blue-600 transition-colors">
                {{ t('faqPage.sections.types.title') }}
              </h2>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="flex-shrink-0 transition-transform duration-300"
                :class="openFaq === 1 ? 'rotate-180' : ''"
              >
                <polyline points="6 9 12 15 18 9"/>
              </svg>
            </button>
            <div
              class="overflow-hidden transition-all duration-300"
              :class="openFaq === 1 ? 'max-h-96' : 'max-h-0'"
            >
              <div class="px-6 md:px-8 pb-6 md:pb-8">
                <p class="text-lg text-gray-700 leading-relaxed">
                  {{ t('faqPage.sections.types.content') }}
                </p>
              </div>
            </div>
          </div>

          <!-- FAQ Item 3: AI Providers -->
          <div
            class="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 hover:border-blue-300/60 transition-all duration-300 shadow-lg hover:shadow-xl animate-slide-up overflow-hidden"
            style="animation-delay: 800ms"
          >
            <button
              @click="toggleFaq(2)"
              class="w-full flex items-center justify-between p-6 md:p-8 text-left group"
            >
              <h2 class="text-xl md:text-2xl font-bold text-gray-900 pr-4 group-hover:text-blue-600 transition-colors">
                {{ t('faqPage.sections.providers.title') }}
              </h2>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="flex-shrink-0 transition-transform duration-300"
                :class="openFaq === 2 ? 'rotate-180' : ''"
              >
                <polyline points="6 9 12 15 18 9"/>
              </svg>
            </button>
            <div
              class="overflow-hidden transition-all duration-300"
              :class="openFaq === 2 ? 'max-h-96' : 'max-h-0'"
            >
              <div class="px-6 md:px-8 pb-6 md:pb-8">
                <p class="text-lg text-gray-700 leading-relaxed">
                  {{ t('faqPage.sections.providers.content') }}
                </p>
              </div>
            </div>
          </div>

          <!-- FAQ Item 4: Privacy -->
          <div
            class="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 hover:border-blue-300/60 transition-all duration-300 shadow-lg hover:shadow-xl animate-slide-up overflow-hidden"
            style="animation-delay: 900ms"
          >
            <button
              @click="toggleFaq(3)"
              class="w-full flex items-center justify-between p-6 md:p-8 text-left group"
            >
              <h2 class="text-xl md:text-2xl font-bold text-gray-900 pr-4 group-hover:text-blue-600 transition-colors">
                {{ t('faqPage.sections.privacy.title') }}
              </h2>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="flex-shrink-0 transition-transform duration-300"
                :class="openFaq === 3 ? 'rotate-180' : ''"
              >
                <polyline points="6 9 12 15 18 9"/>
              </svg>
            </button>
            <div
              class="overflow-hidden transition-all duration-300"
              :class="openFaq === 3 ? 'max-h-96' : 'max-h-0'"
            >
              <div class="px-6 md:px-8 pb-6 md:pb-8">
                <p class="text-lg text-gray-700 leading-relaxed">
                  {{ t('faqPage.sections.privacy.content') }}
                </p>
              </div>
            </div>
          </div>

          <!-- FAQ Item 5: Customize -->
          <div
            class="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 hover:border-blue-300/60 transition-all duration-300 shadow-lg hover:shadow-xl animate-slide-up overflow-hidden"
            style="animation-delay: 1000ms"
          >
            <button
              @click="toggleFaq(4)"
              class="w-full flex items-center justify-between p-6 md:p-8 text-left group"
            >
              <h2 class="text-xl md:text-2xl font-bold text-gray-900 pr-4 group-hover:text-blue-600 transition-colors">
                {{ t('faqPage.sections.customize.title') }}
              </h2>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="flex-shrink-0 transition-transform duration-300"
                :class="openFaq === 4 ? 'rotate-180' : ''"
              >
                <polyline points="6 9 12 15 18 9"/>
              </svg>
            </button>
            <div
              class="overflow-hidden transition-all duration-300"
              :class="openFaq === 4 ? 'max-h-96' : 'max-h-0'"
            >
              <div class="px-6 md:px-8 pb-6 md:pb-8">
                <p class="text-lg text-gray-700 leading-relaxed">
                  {{ t('faqPage.sections.customize.content') }}
                </p>
              </div>
            </div>
          </div>

          <!-- FAQ Item 6: Languages -->
          <div
            class="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 hover:border-blue-300/60 transition-all duration-300 shadow-lg hover:shadow-xl animate-slide-up overflow-hidden"
            style="animation-delay: 1100ms"
          >
            <button
              @click="toggleFaq(5)"
              class="w-full flex items-center justify-between p-6 md:p-8 text-left group"
            >
              <h2 class="text-xl md:text-2xl font-bold text-gray-900 pr-4 group-hover:text-blue-600 transition-colors">
                {{ t('faqPage.sections.languages.title') }}
              </h2>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="flex-shrink-0 transition-transform duration-300"
                :class="openFaq === 5 ? 'rotate-180' : ''"
              >
                <polyline points="6 9 12 15 18 9"/>
              </svg>
            </button>
            <div
              class="overflow-hidden transition-all duration-300"
              :class="openFaq === 5 ? 'max-h-96' : 'max-h-0'"
            >
              <div class="px-6 md:px-8 pb-6 md:pb-8">
                <p class="text-lg text-gray-700 leading-relaxed">
                  {{ t('faqPage.sections.languages.content') }}
                </p>
              </div>
            </div>
          </div>

          <!-- FAQ Item 7: Getting Started -->
          <div
            class="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 hover:border-blue-300/60 transition-all duration-300 shadow-lg hover:shadow-xl animate-slide-up overflow-hidden"
            style="animation-delay: 1200ms"
          >
            <button
              @click="toggleFaq(6)"
              class="w-full flex items-center justify-between p-6 md:p-8 text-left group"
            >
              <h2 class="text-xl md:text-2xl font-bold text-gray-900 pr-4 group-hover:text-blue-600 transition-colors">
                {{ t('faqPage.sections.gettingStarted.title') }}
              </h2>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="flex-shrink-0 transition-transform duration-300"
                :class="openFaq === 6 ? 'rotate-180' : ''"
              >
                <polyline points="6 9 12 15 18 9"/>
              </svg>
            </button>
            <div
              class="overflow-hidden transition-all duration-300"
              :class="openFaq === 6 ? 'max-h-96' : 'max-h-0'"
            >
              <div class="px-6 md:px-8 pb-6 md:pb-8">
                <p class="text-lg text-gray-700 leading-relaxed">
                  {{ t('faqPage.sections.gettingStarted.content') }}
                </p>
              </div>
            </div>
          </div>

          <!-- FAQ Item 8: Accuracy -->
          <div
            class="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 hover:border-blue-300/60 transition-all duration-300 shadow-lg hover:shadow-xl animate-slide-up overflow-hidden"
            style="animation-delay: 1300ms"
          >
            <button
              @click="toggleFaq(7)"
              class="w-full flex items-center justify-between p-6 md:p-8 text-left group"
            >
              <h2 class="text-xl md:text-2xl font-bold text-gray-900 pr-4 group-hover:text-blue-600 transition-colors">
                {{ t('faqPage.sections.accuracy.title') }}
              </h2>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="flex-shrink-0 transition-transform duration-300"
                :class="openFaq === 7 ? 'rotate-180' : ''"
              >
                <polyline points="6 9 12 15 18 9"/>
              </svg>
            </button>
            <div
              class="overflow-hidden transition-all duration-300"
              :class="openFaq === 7 ? 'max-h-96' : 'max-h-0'"
            >
              <div class="px-6 md:px-8 pb-6 md:pb-8">
                <p class="text-lg text-gray-700 leading-relaxed">
                  {{ t('faqPage.sections.accuracy.content') }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- CTA Section -->
        <div class="mt-16 max-w-4xl mx-auto animate-slide-up" style="animation-delay: 1400ms">
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 md:p-12 border border-blue-100 text-center">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">{{ t('faqPage.cta.title') }}</h2>
            <p class="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              {{ t('faqPage.cta.description') }}
            </p>
            <a
              href="https://chrome.google.com/webstore/detail/mhhcjilkgnegleamofnbeacpnblblkhn"
              target="_blank"
              rel="noopener noreferrer"
              class="inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white bg-blue-600 rounded-xl hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl group"
            >
              {{ t('faqPage.cta.button') }}
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link ml-2 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                <polyline points="15,3 21,3 21,9"/>
                <line x1="10" x2="21" y1="14" y2="3"/>
              </svg>
            </a>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useSeo } from '~/composables/useSeo'

const { t } = useI18n()
const { setSeoMeta } = useSeo()

// Set SEO meta tags for FAQ page
setSeoMeta({ i18nKey: 'faqPage' })

// FAQ accordion state
const openFaq = ref<number | null>(0) // Open first FAQ by default

const toggleFaq = (index: number) => {
  openFaq.value = openFaq.value === index ? null : index
}

// Add structured data for FAQ page
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'FAQPage',
        'mainEntity': [
          {
            '@type': 'Question',
            'name': t('faqPage.sections.what.title'),
            'acceptedAnswer': {
              '@type': 'Answer',
              'text': t('faqPage.sections.what.content')
            }
          },
          {
            '@type': 'Question',
            'name': t('faqPage.sections.types.title'),
            'acceptedAnswer': {
              '@type': 'Answer',
              'text': t('faqPage.sections.types.content')
            }
          },
          {
            '@type': 'Question',
            'name': t('faqPage.sections.providers.title'),
            'acceptedAnswer': {
              '@type': 'Answer',
              'text': t('faqPage.sections.providers.content')
            }
          },
          {
            '@type': 'Question',
            'name': t('faqPage.sections.privacy.title'),
            'acceptedAnswer': {
              '@type': 'Answer',
              'text': t('faqPage.sections.privacy.content')
            }
          },
          {
            '@type': 'Question',
            'name': t('faqPage.sections.customize.title'),
            'acceptedAnswer': {
              '@type': 'Answer',
              'text': t('faqPage.sections.customize.content')
            }
          },
          {
            '@type': 'Question',
            'name': t('faqPage.sections.languages.title'),
            'acceptedAnswer': {
              '@type': 'Answer',
              'text': t('faqPage.sections.languages.content')
            }
          },
          {
            '@type': 'Question',
            'name': t('faqPage.sections.gettingStarted.title'),
            'acceptedAnswer': {
              '@type': 'Answer',
              'text': t('faqPage.sections.gettingStarted.content')
            }
          },
          {
            '@type': 'Question',
            'name': t('faqPage.sections.accuracy.title'),
            'acceptedAnswer': {
              '@type': 'Answer',
              'text': t('faqPage.sections.accuracy.content')
            }
          }
        ]
      })
    }
  ]
})
</script>

<style scoped>
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out forwards;
  opacity: 0;
}
</style>
