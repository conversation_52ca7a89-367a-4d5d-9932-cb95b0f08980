<template>
  <div class="how-it-works-page">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-50 to-cyan-50 pt-32 pb-16 md:pt-40 md:pb-24">
      <div class="container mx-auto px-4 max-w-4xl">
        <div class="text-center">
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-gray-900">
            {{ t('howItWorks.title') }}
          </h1>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            {{ t('howItWorks.subtitle') }}
          </p>
        </div>
      </div>
    </section>

    <!-- How It Works Steps -->
    <section class="py-16">
      <div class="container mx-auto px-4 max-w-5xl">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Step 1 -->
          <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
              <span class="text-blue-600 font-bold text-xl">1</span>
            </div>
            <h2 class="text-2xl font-bold mb-4 text-gray-900">{{ t('howItWorks.steps.install.title') }}</h2>
            <p class="text-gray-700 mb-4">
              {{ t('howItWorks.steps.install.description') }}
            </p>
            <ul class="list-disc pl-5 text-gray-600 space-y-1">
              <li v-for="(feature, index) in t('howItWorks.steps.install.features', { returnObjects: true })" :key="index">{{ feature }}</li>
            </ul>
          </div>

          <!-- Step 2 -->
          <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
              <span class="text-blue-600 font-bold text-xl">2</span>
            </div>
            <h2 class="text-2xl font-bold mb-4 text-gray-900">{{ t('howItWorks.steps.configure.title') }}</h2>
            <p class="text-gray-700 mb-4">
              {{ t('howItWorks.steps.configure.description') }}
            </p>
            <ul class="list-disc pl-5 text-gray-600 space-y-1">
              <li v-for="(feature, index) in t('howItWorks.steps.configure.features', { returnObjects: true })" :key="index">{{ feature }}</li>
            </ul>
          </div>

          <!-- Step 3 -->
          <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
              <span class="text-blue-600 font-bold text-xl">3</span>
            </div>
            <h2 class="text-2xl font-bold mb-4 text-gray-900">{{ t('howItWorks.steps.describe.title') }}</h2>
            <p class="text-gray-700 mb-4">
              {{ t('howItWorks.steps.describe.description') }}
            </p>
            <ul class="list-disc pl-5 text-gray-600 space-y-1">
              <li v-for="(feature, index) in t('howItWorks.steps.describe.features', { returnObjects: true })" :key="index">{{ feature }}</li>
            </ul>
          </div>

          <!-- Step 4 -->
          <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
              <span class="text-blue-600 font-bold text-xl">4</span>
            </div>
            <h2 class="text-2xl font-bold mb-4 text-gray-900">{{ t('howItWorks.steps.aiFill.title') }}</h2>
            <p class="text-gray-700 mb-4">
              {{ t('howItWorks.steps.aiFill.description') }}
            </p>
            <ul class="list-disc pl-5 text-gray-600 space-y-1">
              <li v-for="(feature, index) in t('howItWorks.steps.aiFill.features', { returnObjects: true })" :key="index">{{ feature }}</li>
            </ul>
          </div>

          <!-- Step 5 -->
          <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
              <span class="text-blue-600 font-bold text-xl">5</span>
            </div>
            <h2 class="text-2xl font-bold mb-4 text-gray-900">{{ t('howItWorks.steps.review.title') }}</h2>
            <p class="text-gray-700 mb-4">
              {{ t('howItWorks.steps.review.description') }}
            </p>
            <ul class="list-disc pl-5 text-gray-600 space-y-1">
              <li v-for="(feature, index) in t('howItWorks.steps.review.features', { returnObjects: true })" :key="index">{{ feature }}</li>
            </ul>
          </div>

          <!-- Step 6 -->
          <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
              <span class="text-blue-600 font-bold text-xl">6</span>
            </div>
            <h2 class="text-2xl font-bold mb-4 text-gray-900">{{ t('howItWorks.steps.saveTime.title') }}</h2>
            <p class="text-gray-700 mb-4">
              {{ t('howItWorks.steps.saveTime.description') }}
            </p>
            <ul class="list-disc pl-5 text-gray-600 space-y-1">
              <li v-for="(feature, index) in t('howItWorks.steps.saveTime.features', { returnObjects: true })" :key="index">{{ feature }}</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Detailed Description Section -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4 max-w-4xl">
        <h2 class="text-3xl md:text-4xl font-bold mb-8 text-center text-gray-900">{{ t('howItWorks.technology.title') }}</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div class="bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
            <h3 class="text-xl font-bold mb-4 text-gray-900">{{ t('howItWorks.technology.sections.aiAnalysis.title') }}</h3>
            <p class="text-gray-700">
              {{ t('howItWorks.technology.sections.aiAnalysis.description') }}
            </p>
          </div>
          
          <div class="bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
            <h3 class="text-xl font-bold mb-4 text-gray-900">{{ t('howItWorks.technology.sections.nlp.title') }}</h3>
            <p class="text-gray-700">
              {{ t('howItWorks.technology.sections.nlp.description') }}
            </p>
          </div>
          
          <div class="bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
            <h3 class="text-xl font-bold mb-4 text-gray-900">{{ t('howItWorks.technology.sections.privacy.title') }}</h3>
            <p class="text-gray-700">
              {{ t('howItWorks.technology.sections.privacy.description') }}
            </p>
          </div>
          
          <div class="bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
            <h3 class="text-xl font-bold mb-4 text-gray-900">{{ t('howItWorks.technology.sections.adaptive.title') }}</h3>
            <p class="text-gray-700">
              {{ t('howItWorks.technology.sections.adaptive.description') }}
            </p>
          </div>
        </div>
        
        <div class="bg-white p-8 rounded-2xl shadow-sm border border-gray-100">
          <h3 class="text-2xl font-bold mb-4 text-gray-900">Supported Form Types</h3>
          <p class="text-gray-700 mb-6">
            Fillify works with a variety of form types across different industries and use cases:
          </p>
          <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div class="flex items-center">
              <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              <span class="text-gray-700">Job Applications</span>
            </div>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              <span class="text-gray-700">Contact Forms</span>
            </div>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              <span class="text-gray-700">Registration Forms</span>
            </div>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              <span class="text-gray-700">Surveys</span>
            </div>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              <span class="text-gray-700">Checkout Forms</span>
            </div>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              <span class="text-gray-700">Support Tickets</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16">
      <div class="container mx-auto px-4 max-w-4xl text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-6 text-gray-900">{{ t('howItWorks.cta.title') }}</h2>
        <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          {{ t('howItWorks.cta.description') }}
        </p>
        <a
          href="https://chrome.google.com/webstore/detail/mhhcjilkgnegleamofnbeacpnblblkhn"
          target="_blank"
          rel="noopener noreferrer"
          class="inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white bg-gradient-to-r from-blue-600 to-cyan-600 rounded-2xl hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-lg hover:shadow-xl"
        >
          {{ t('howItWorks.cta.button') }}
        </a>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useSeo } from '~/composables/useSeo'

const { t } = useI18n()
const { setSeoMeta } = useSeo()

// Set SEO meta tags for how-it-works page
setSeoMeta({ i18nKey: 'howItWorks' })

// Add structured data for How-To page (GEO optimization)
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'HowTo',
        'name': t('howItWorks.title'),
        'description': t('howItWorks.subtitle'),
        'image': 'https://fillify.tech/og/og-image.png',
        'totalTime': 'PT10M',
        'estimatedCost': {
          '@type': 'MonetaryAmount',
          'currency': 'USD',
          'value': '0'
        },
        'tool': [
          {
            '@type': 'HowToTool',
            'name': 'Fillify Chrome Extension'
          },
          {
            '@type': 'HowToTool',
            'name': 'AI Provider API Key'
          }
        ],
        'step': [
          {
            '@type': 'HowToStep',
            'name': t('howItWorks.steps.install.title'),
            'text': t('howItWorks.steps.install.description')
          },
          {
            '@type': 'HowToStep',
            'name': t('howItWorks.steps.configure.title'),
            'text': t('howItWorks.steps.configure.description')
          },
          {
            '@type': 'HowToStep',
            'name': t('howItWorks.steps.describe.title'),
            'text': t('howItWorks.steps.describe.description')
          },
          {
            '@type': 'HowToStep',
            'name': t('howItWorks.steps.aiFill.title'),
            'text': t('howItWorks.steps.aiFill.description')
          },
          {
            '@type': 'HowToStep',
            'name': t('howItWorks.steps.review.title'),
            'text': t('howItWorks.steps.review.description')
          },
          {
            '@type': 'HowToStep',
            'name': t('howItWorks.steps.saveTime.title'),
            'text': t('howItWorks.steps.saveTime.description')
          }
        ],
        'mainEntityOfPage': 'https://fillify.tech/how-it-works'
      })
    }
  ]
})
</script>

<style scoped>
.how-it-works-page {
  min-height: 100vh;
}
</style>