<template>
  <div>
    <!-- Main Content -->
    <main class="relative z-10">
      <!-- Hero Section (Centered Layout) -->
      <section class="relative pt-32 pb-20 md:pt-48 md:pb-32 overflow-hidden">
        <!-- Background Elements -->
        <!-- Background Elements Removed for Continuity -->

        <div class="container mx-auto px-4 relative z-10">
          <div class="max-w-4xl mx-auto text-center relative">
            <!-- Floating Elements (Orbiting) -->
            <!-- Top Left: Profile -->
            <div class="absolute -top-16 -left-8 lg:-left-40 animate-float-delay-0 hidden md:block perspective-1000">
              <div class="relative transform -rotate-6 hover:rotate-0 transition-transform duration-500 group">
                <HeroFloatingElement type="profile" :icon="User" />
              </div>
            </div>

            <!-- Top Right: Bug + Sparkles -->
            <div class="absolute top-40 -right-12 lg:-right-48 animate-float-delay-500 hidden md:block perspective-1000">
              <div class="relative transform rotate-6 hover:rotate-0 transition-transform duration-500">
                <HeroFloatingElement type="bug" :icon="Bug" />
                <!-- Decorative Sparkles -->
                <div class="absolute -top-6 -right-6 text-yellow-400 animate-pulse-slow">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L14.4 9.6L22 12L14.4 14.4L12 22L9.6 14.4L2 12L9.6 9.6L12 2Z" />
                  </svg>
                </div>
                <div class="absolute bottom-8 -left-8 text-blue-400 animate-pulse-slow" style="animation-delay: 1s;">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L14.4 9.6L22 12L14.4 14.4L12 22L9.6 14.4L2 12L9.6 9.6L12 2Z" />
                  </svg>
                </div>
              </div>
            </div>

            <!-- Bottom Left: Email + Badge -->
            <div class="absolute bottom-0 -left-12 lg:-left-24 animate-float-delay-700 hidden md:block perspective-1000">
              <div class="relative transform -rotate-3 hover:rotate-0 transition-transform duration-500">
                <HeroFloatingElement type="email" :icon="Mail" />
                <!-- Decorative Badge -->
                <div class="absolute -top-4 -right-4 transform rotate-6 animate-bounce-slow">
                  <HeroBadge text="Auto-filled" />
                </div>
              </div>
            </div>

            <!-- Badge -->
            <div class="inline-flex animate-slide-up justify-center mb-8">
              <div class="flex items-center gap-2 px-4 py-2 text-sm rounded-full border border-gray-200 bg-white/80 text-gray-600 font-medium backdrop-blur-md shadow-sm">
                <span class="relative flex h-2.5 w-2.5">
                  <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                  <span class="relative inline-flex rounded-full h-2.5 w-2.5 bg-green-500"></span>
                </span>
                {{ browserStoreText }}
              </div>
            </div>

            <!-- Heading -->
            <h1 class="text-6xl md:text-8xl font-extrabold tracking-tight text-gray-900 animate-slide-up [animation-delay:200ms] mb-8 leading-[1.1]">
              {{ t('hero.title.text') }}
              <span class="bg-gradient-to-r from-blue-600 to-indigo-600 text-transparent bg-clip-text">
                {{ t('hero.title.staticWord') }}
              </span>
            </h1>

            <!-- Description -->
            <p class="text-xl md:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed animate-slide-up [animation-delay:400ms] font-normal">
              {{ t('hero.description') }}
            </p>

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up [animation-delay:600ms]">
              <a
                href="https://chrome.google.com/webstore/detail/mhhcjilkgnegleamofnbeacpnblblkhn"
                target="_blank"
                rel="noopener noreferrer"
                class="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-gray-900 rounded-xl hover:bg-gray-800 transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-1 min-w-[200px]"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 48 48"
                  class="w-7 h-7 mr-3 flex-shrink-0"
                  role="img"
                  aria-label="Chrome Browser Logo"
                >
                  <path fill="#fff" d="M34,24c0,5.521-4.479,10-10,10s-10-4.479-10-10s4.479-10,10-10S34,18.479,34,24z"/>
                  <linearGradient id="Pax8JcnMzivu8f~SZ~k1ya" x1="5.789" x2="31.324" y1="34.356" y2="20.779" gradientTransform="matrix(1 0 0 -1 0 50)" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stop-color="#4caf50"/>
                    <stop offset=".489" stop-color="#4aaf50"/>
                    <stop offset=".665" stop-color="#43ad50"/>
                    <stop offset=".79" stop-color="#38aa50"/>
                    <stop offset=".892" stop-color="#27a550"/>
                    <stop offset=".978" stop-color="#11a050"/>
                    <stop offset="1" stop-color="#0a9e50"/>
                  </linearGradient>
                  <path fill="url(#Pax8JcnMzivu8f~SZ~k1ya)" d="M31.33,29.21l-8.16,14.77C12.51,43.55,4,34.76,4,24C4,12.96,12.96,4,24,4v11 c-4.97,0-9,4.03-9,9s4.03,9,9,9C27.03,33,29.7,31.51,31.33,29.21z"/>
                  <linearGradient id="Pax8JcnMzivu8f~SZ~k1yb" x1="33.58" x2="33.58" y1="6" y2="34.797" gradientTransform="matrix(1 0 0 -1 0 50)" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stop-color="#ffd747"/>
                    <stop offset=".482" stop-color="#ffd645"/>
                    <stop offset=".655" stop-color="#fed43e"/>
                    <stop offset=".779" stop-color="#fccf33"/>
                    <stop offset=".879" stop-color="#fac922"/>
                    <stop offset=".964" stop-color="#f7c10c"/>
                    <stop offset=".964" stop-color="#f7c10c"/>
                    <stop offset="1" stop-color="#f5bc00"/>
                  </linearGradient>
                  <path fill="url(#Pax8JcnMzivu8f~SZ~k1yb)" d="M44,24c0,11.05-8.95,20-20,20h-0.84l8.17-14.79C32.38,27.74,33,25.94,33,24 c0-4.97-4.03-9-9-9V4c7.81,0,14.55,4.48,17.85,11C43.21,17.71,44,20.76,44,24z"/>
                  <linearGradient id="Pax8JcnMzivu8f~SZ~k1yc" x1="36.128" x2="11.574" y1="44.297" y2="28.954" gradientTransform="matrix(1 0 0 -1 0 50)" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stop-color="#f7572f"/>
                    <stop offset=".523" stop-color="#f7552d"/>
                    <stop offset=".712" stop-color="#f75026"/>
                    <stop offset=".846" stop-color="#f7461b"/>
                    <stop offset=".954" stop-color="#f7390a"/>
                    <stop offset="1" stop-color="#f73100"/>
                  </linearGradient>
                  <path fill="url(#Pax8JcnMzivu8f~SZ~k1yc)" d="M41.84,15H24c-4.97,0-9,4.03-9,9c0,1.49,0.36,2.89,1.01,4.13H16L7.16,13.26H7.14 C10.68,7.69,16.91,4,24,4C31.8,4,38.55,8.48,41.84,15z"/>
                  <linearGradient id="Pax8JcnMzivu8f~SZ~k1yd" x1="19.05" x2="28.95" y1="30.95" y2="21.05" gradientTransform="matrix(1 0 0 -1 0 50)" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stop-color="#2aa4f4"/>
                    <stop offset="1" stop-color="#007ad9"/>
                  </linearGradient>
                  <path fill="url(#Pax8JcnMzivu8f~SZ~k1yd)" d="M31,24c0,3.867-3.133,7-7,7s-7-3.133-7-7s3.133-7,7-7S31,20.133,31,24z"/>
                </svg>
                {{ t('hero.cta.chrome') }}
              </a>
              <a
                href="https://microsoftedge.microsoft.com/addons/detail/ieedpolbpalhomefmggdickoicpoodab"
                target="_blank"
                rel="noopener noreferrer"
                class="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-gray-700 bg-white border border-gray-200 rounded-xl hover:bg-gray-50 transition-all duration-300 shadow-sm hover:shadow-md hover:-translate-y-1 min-w-[200px]"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 256 256"
                  class="w-7 h-7 mr-3 flex-shrink-0"
                  role="img"
                  aria-label="Microsoft Edge Logo"
                >
                  <defs>
                    <radialGradient id="b" cx="161.8" cy="68.9" r="95.4" gradientTransform="matrix(1 0 0 -.95 0 248.8)" gradientUnits="userSpaceOnUse">
                      <stop offset=".7" stop-opacity="0"/>
                      <stop offset=".9" stop-opacity=".5"/>
                      <stop offset="1"/>
                    </radialGradient>
                    <radialGradient id="d" cx="-340.3" cy="63" r="143.2" gradientTransform="matrix(.15 -.99 -.8 -.12 176.6 -125.4)" gradientUnits="userSpaceOnUse">
                      <stop offset=".8" stop-opacity="0"/>
                      <stop offset=".9" stop-opacity=".5"/>
                      <stop offset="1"/>
                    </radialGradient>
                    <radialGradient id="e" cx="113.4" cy="570.2" r="202.4" gradientTransform="matrix(-.04 1 2.13 .08 -1179.5 -106.7)" gradientUnits="userSpaceOnUse">
                      <stop offset="0" stop-color="#35c1f1"/>
                      <stop offset=".1" stop-color="#34c1ed"/>
                      <stop offset=".2" stop-color="#2fc2df"/>
                      <stop offset=".3" stop-color="#2bc3d2"/>
                      <stop offset=".7" stop-color="#36c752"/>
                    </radialGradient>
                    <radialGradient id="f" cx="376.5" cy="568" r="97.3" gradientTransform="matrix(.28 .96 .78 -.23 -303.8 -148.5)" gradientUnits="userSpaceOnUse">
                      <stop offset="0" stop-color="#66eb6e"/>
                      <stop offset="1" stop-color="#66eb6e" stop-opacity="0"/>
                    </radialGradient>
                    <linearGradient id="a" x1="63.3" y1="84" x2="241.7" y2="84" gradientTransform="matrix(1 0 0 -1 0 266)" gradientUnits="userSpaceOnUse">
                      <stop offset="0" stop-color="#0c59a4"/>
                      <stop offset="1" stop-color="#114a8b"/>
                    </linearGradient>
                    <linearGradient id="c" x1="157.3" y1="161.4" x2="46" y2="40.1" gradientTransform="matrix(1 0 0 -1 0 266)" gradientUnits="userSpaceOnUse">
                      <stop offset="0" stop-color="#1b9de2"/>
                      <stop offset=".2" stop-color="#1595df"/>
                      <stop offset=".7" stop-color="#0680d7"/>
                      <stop offset="1" stop-color="#0078d4"/>
                    </linearGradient>
                  </defs>
                  <path d="M235.7 195.5a93.7 93.7 0 0 1-10.6 4.7 101.9 101.9 0 0 1-35.9 6.4c-47.3 0-88.5-32.5-88.5-74.3a31.5 31.5 0 0 1 16.4-27.3c-42.8 1.8-53.8 46.4-53.8 72.5 0 74 68.1 81.4 82.8 81.4 7.9 0 19.8-2.3 27-4.6l1.3-.4a128.3 128.3 0 0 0 66.6-52.8 4 4 0 0 0-5.3-5.6Z" transform="translate(-4.6 -5)" style="fill:url(#a)"/>
                  <path d="M235.7 195.5a93.7 93.7 0 0 1-10.6 4.7 101.9 101.9 0 0 1-35.9 6.4c-47.3 0-88.5-32.5-88.5-74.3a31.5 31.5 0 0 1 16.4-27.3c-42.8 1.8-53.8 46.4-53.8 72.5 0 74 68.1 81.4 82.8 81.4 7.9 0 19.8-2.3 27-4.6l1.3-.4a128.3 128.3 0 0 0 66.6-52.8 4 4 0 0 0-5.3-5.6Z" transform="translate(-4.6 -5)" style="isolation:isolate;opacity:.35;fill:url(#b)"/>
                  <path d="M110.3 246.3A79.2 79.2 0 0 1 87.6 225a80.7 80.7 0 0 1 29.5-120c3.2-1.5 8.5-4.1 15.6-4a32.4 32.4 0 0 1 25.7 13 31.9 31.9 0 0 1 6.3 18.7c0-.2 24.5-79.6-80-79.6-43.9 0-80 41.6-80 78.2a130.2 130.2 0 0 0 12.1 56 128 128 0 0 0 156.4 67 75.5 75.5 0 0 1-62.8-8Z" transform="translate(-4.6 -5)" style="fill:url(#c)"/>
                  <path d="M110.3 246.3A79.2 79.2 0 0 1 87.6 225a80.7 80.7 0 0 1 29.5-120c3.2-1.5 8.5-4.1 15.6-4a32.4 32.4 0 0 1 25.7 13 31.9 31.9 0 0 1 6.3 18.7c0-.2 24.5-79.6-80-79.6-43.9 0-80 41.6-80 78.2a130.2 130.2 0 0 0 12.1 56 128 128 0 0 0 156.4 67 75.5 75.5 0 0 1-62.8-8Z" transform="translate(-4.6 -5)" style="opacity:.41;fill:url(#d);isolation:isolate"/>
                  <path d="M157 153.8c-.9 1-3.4 2.5-3.4 5.6 0 2.6 1.7 5.2 4.8 7.3 14.3 10 41.4 8.6 41.5 8.6a59.6 59.6 0 0 0 30.3-8.3 61.4 61.4 0 0 0 30.4-52.9c.3-22.4-8-37.3-11.3-43.9C228 28.8 182.3 5 132.6 5a128 128 0 0 0-128 126.2c.5-36.5 36.8-66 80-66 3.5 0 23.5.3 42 10a72.6 72.6 0 0 1 30.9 29.3c6.1 10.6 7.2 24.1 7.2 29.5s-2.7 13.3-7.8 19.9Z" transform="translate(-4.6 -5)" style="fill:url(#e)"/>
                  <path d="M157 153.8c-.9 1-3.4 2.5-3.4 5.6 0 2.6 1.7 5.2 4.8 7.3 14.3 10 41.4 8.6 41.5 8.6a59.6 59.6 0 0 0 30.3-8.3 61.4 61.4 0 0 0 30.4-52.9c.3-22.4-8-37.3-11.3-43.9C228 28.8 182.3 5 132.6 5a128 128 0 0 0-128 126.2c.5-36.5 36.8-66 80-66 3.5 0 23.5.3 42 10a72.6 72.6 0 0 1 30.9 29.3c6.1 10.6 7.2 24.1 7.2 29.5s-2.7 13.3-7.8 19.9Z" transform="translate(-4.6 -5)" style="fill:url(#f)"/>
                </svg>
                {{ t('hero.cta.edge') }}
              </a>
            </div>
            
            <!-- Social Proof -->
            <div class="pt-12 flex justify-center items-center gap-8 opacity-60 grayscale hover:grayscale-0 transition-all duration-500 animate-slide-up [animation-delay:700ms]">
               <img src="https://cdn-b.saashub.com/img/badges/approved-color.png?v=1" class="h-8 object-contain" alt="SaaSHub" />
               <img src="https://startupfa.me/badges/featured-badge.webp" class="h-8 object-contain" alt="Startup Fame" />
               <img src="https://twelve.tools/badge0-light.svg" class="h-8 object-contain" alt="Twelve Tools" />
            </div>
          </div>

          <!-- Demo -->
          <div class="mt-24 max-w-6xl mx-auto animate-slide-up [animation-delay:800ms]" ref="chromeDemoContainer">
            <div class="relative bg-white rounded-2xl shadow-2xl border border-gray-200/50 overflow-hidden ring-1 ring-gray-900/5 aspect-video">
              <FullChromeDemo :shouldStartAnimation="shouldStartDemoAnimation" />
            </div>
          </div>
        </div>
      </section>

      <!-- Features Section (Stacked Cards) -->
      <section id="features" class="py-24" ref="featuresSection">
        <div class="container mx-auto px-4">
          <div class="text-center mb-24 max-w-3xl mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold mb-6 text-gray-900">
              {{ t('features.title') }}
            </h2>
            <p class="text-xl text-gray-600 font-light">
              {{ t('features.subtitle') }}
            </p>
          </div>

          <div class="">
            <StackedFeatureCard
              v-for="(feature, index) in features"
              :key="feature.id"
              :index="index"
              :totalCards="features.length"
              :translationKey="feature.translationKey"
              :title="t(`features.${feature.translationKey}.title`)"
              :description="t(`features.${feature.translationKey}.description`)"
              :icon="feature.icon"
              :demoComponent="feature.demoComponent"
              :reverse="feature.reverse"
            />
          </div>
        </div>
      </section>

      <!-- FAQs Section -->
      <section class="container mx-auto px-4 py-16 md:py-24" ref="faqSection">
        <div class="max-w-3xl mx-auto">
          <h2 class="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900">
            {{ t('faq.title') }}
          </h2>

          <div class="space-y-4">
            <!-- Question 1 -->
            <div class="faq-item group border-b border-gray-200 pb-4">
              <button
                class="w-full py-4 text-left focus:outline-none flex items-center justify-between"
                @click="toggleFaq(0)"
              >
                <h3 class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">{{ t('faq.items.what.question') }}</h3>
                <span class="ml-4 flex-shrink-0 text-gray-400 group-hover:text-blue-600 transition-colors">
                  <svg
                    class="w-5 h-5 transform transition-transform duration-300"
                    :class="openFaq.includes(0) ? 'rotate-180' : ''"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </button>
              <div
                class="transition-all duration-300 ease-in-out overflow-hidden"
                :class="openFaq.includes(0) ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'"
              >
                <div class="pb-4 text-gray-600 leading-relaxed">
                  {{ t('faq.items.what.answer') }}
                </div>
              </div>
            </div>

            <!-- Question 2 -->
            <div class="faq-item group border-b border-gray-200 pb-4">
              <button
                class="w-full py-4 text-left focus:outline-none flex items-center justify-between"
                @click="toggleFaq(1)"
              >
                <h3 class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">{{ t('faq.items.types.question') }}</h3>
                <span class="ml-4 flex-shrink-0 text-gray-400 group-hover:text-blue-600 transition-colors">
                  <svg
                    class="w-5 h-5 transform transition-transform duration-300"
                    :class="openFaq.includes(1) ? 'rotate-180' : ''"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </button>
              <div
                class="transition-all duration-300 ease-in-out overflow-hidden"
                :class="openFaq.includes(1) ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'"
              >
                <div class="pb-4 text-gray-600 leading-relaxed">
                  {{ t('faq.items.types.answer') }}
                </div>
              </div>
            </div>

            <!-- Question 3 -->
            <div class="faq-item group border-b border-gray-200 pb-4">
              <button
                class="w-full py-4 text-left focus:outline-none flex items-center justify-between"
                @click="toggleFaq(2)"
              >
                <h3 class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">{{ t('faq.items.providers.question') }}</h3>
                <span class="ml-4 flex-shrink-0 text-gray-400 group-hover:text-blue-600 transition-colors">
                  <svg
                    class="w-5 h-5 transform transition-transform duration-300"
                    :class="openFaq.includes(2) ? 'rotate-180' : ''"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </button>
              <div
                class="transition-all duration-300 ease-in-out overflow-hidden"
                :class="openFaq.includes(2) ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'"
              >
                <div class="pb-4 text-gray-600 leading-relaxed">
                  {{ t('faq.items.providers.answer') }}
                </div>
              </div>
            </div>

            <!-- Question 4 -->
            <div class="faq-item group border-b border-gray-200 pb-4">
              <button
                class="w-full py-4 text-left focus:outline-none flex items-center justify-between"
                @click="toggleFaq(3)"
              >
                <h3 class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">{{ t('faq.items.privacy.question') }}</h3>
                <span class="ml-4 flex-shrink-0 text-gray-400 group-hover:text-blue-600 transition-colors">
                  <svg
                    class="w-5 h-5 transform transition-transform duration-300"
                    :class="openFaq.includes(3) ? 'rotate-180' : ''"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </button>
              <div
                class="transition-all duration-300 ease-in-out overflow-hidden"
                :class="openFaq.includes(3) ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'"
              >
                <div class="pb-4 text-gray-600 leading-relaxed">
                  {{ t('faq.items.privacy.answer') }}
                </div>
              </div>
            </div>

            <!-- Question 5 -->
            <div class="faq-item group border-b border-gray-200 pb-4">
              <button
                class="w-full py-4 text-left focus:outline-none flex items-center justify-between"
                @click="toggleFaq(4)"
              >
                <h3 class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">{{ t('faq.items.customize.question') }}</h3>
                <span class="ml-4 flex-shrink-0 text-gray-400 group-hover:text-blue-600 transition-colors">
                  <svg
                    class="w-5 h-5 transform transition-transform duration-300"
                    :class="openFaq.includes(4) ? 'rotate-180' : ''"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </button>
              <div
                class="transition-all duration-300 ease-in-out overflow-hidden"
                :class="openFaq.includes(4) ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'"
              >
                <div class="pb-4 text-gray-600 leading-relaxed">
                  {{ t('faq.items.customize.answer') }}
                </div>
              </div>
            </div>

            <!-- Question 6 -->
            <div class="faq-item group border-b border-gray-200 pb-4">
              <button
                class="w-full py-4 text-left focus:outline-none flex items-center justify-between"
                @click="toggleFaq(5)"
              >
                <h3 class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">{{ t('faq.items.languages.question') }}</h3>
                <span class="ml-4 flex-shrink-0 text-gray-400 group-hover:text-blue-600 transition-colors">
                  <svg
                    class="w-5 h-5 transform transition-transform duration-300"
                    :class="openFaq.includes(5) ? 'rotate-180' : ''"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </button>
              <div
                class="transition-all duration-300 ease-in-out overflow-hidden"
                :class="openFaq.includes(5) ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'"
              >
                <div class="pb-4 text-gray-600 leading-relaxed">
                  {{ t('faq.items.languages.answer') }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Bottom CTA Section -->
      <section class="container mx-auto px-4 py-16 md:py-24">
        <div class="relative overflow-hidden rounded-3xl bg-gray-900 max-w-5xl mx-auto shadow-2xl" ref="ctaSection">
          <!-- Background effects -->
          <div class="absolute inset-0 bg-[url('https://grainy-gradients.vercel.app/noise.svg')] opacity-10 mix-blend-soft-light"></div>
          <div class="absolute top-0 right-0 w-[500px] h-[500px] bg-blue-500/20 rounded-full blur-3xl -translate-y-1/2 translate-x-1/4"></div>
          <div class="absolute bottom-0 left-0 w-[400px] h-[400px] bg-indigo-500/20 rounded-full blur-3xl translate-y-1/3 -translate-x-1/4"></div>

          <!-- Content -->
          <div class="relative px-8 py-20 md:px-16 md:py-24 text-center">
            <div class="max-w-3xl mx-auto space-y-8">
              <h2 class="text-4xl md:text-5xl font-bold text-white leading-tight tracking-tight">
                {{ t('bottomCta.title') }}
              </h2>
              <p class="text-xl text-gray-400">
                {{ t('bottomCta.subtitle') }}
              </p>
              
              <div class="flex flex-col sm:flex-row gap-5 justify-center pt-4">
                <a
                  :href="browserStoreUrl"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-gray-900 bg-white rounded-xl hover:bg-gray-50 transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-1 group min-w-[200px]"
                >
                  <span class="flex items-center">
                    {{ t('bottomCta.button') }}
                    <ArrowRight class="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ArrowRight, ArrowDown, ScanLine, ToggleLeft, Zap, Settings, Chrome, Mail, Bug, User } from 'lucide-vue-next'
import { useIntersectionObserver } from '@vueuse/core'
import StackedFeatureCard from '~/components/features/StackedFeatureCard.vue'
import HeroFloatingElement from '~/components/hero/HeroFloatingElement.vue'
import HeroCursor from '~/components/hero/HeroCursor.vue'
import HeroBadge from '~/components/hero/HeroBadge.vue'
import FormDetectionDemo from '~/components/features/FormDetectionDemo.vue'
import AIProvidersDemo from '~/components/features/AIProvidersDemo.vue'
import ModeSwitcherDemo from '~/components/features/ModeSwitcherDemo.vue'
import AutoFillDemo from '~/components/features/AutoFillDemo.vue'
import FullChromeDemo from '~/components/demo/FullChromeDemo.vue'
import { useSeo } from '~/composables/useSeo'

const { t } = useI18n()
const { setSeoMeta } = useSeo()

// Set SEO meta tags for home page
setSeoMeta()

// Add structured data for GEO optimization
const generateStructuredData = () => {
  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    'name': 'Fillify',
    'description': t('seo.description') || 'Fillify revolutionizes form filling with AI technology. Automatically complete web forms, compose emails, and generate bug reports with intelligent automation.',
    'applicationCategory': 'UtilitiesApplication',
    'operatingSystem': 'Chrome, Edge',
    'offers': {
      '@type': 'Offer',
      'price': '0',
      'priceCurrency': 'USD'
    },
    'publisher': {
      '@type': 'Organization',
      'name': 'Fillify'
    },
    'featureList': [
      'AI-Powered Form Filling',
      'Email Composition',
      'Bug Report Generation',
      'Multi-language Support',
      'Privacy Protection'
    ],
    'keywords': 'AI Form Filling, AI Automation, AI Email Generation, AI Bug Report Generation, Smart Form Completion, Automated Data Entry, AI Form Assistant, Intelligent Form Filling, Chrome Form Autofill, AI Form Filler',
    'isAccessibleForFree': true,
    'url': 'https://fillify.tech',
    'mainEntity': generateFAQSchema() // Include FAQ schema as main entity
  }
}

// Generate FAQ schema for GEO optimization
const generateFAQSchema = () => {
  const faqItems = [
    {
      question: t('faq.items.what.question'),
      answer: t('faq.items.what.answer')
    },
    {
      question: t('faq.items.types.question'),
      answer: t('faq.items.types.answer')
    },
    {
      question: t('faq.items.providers.question'),
      answer: t('faq.items.providers.answer')
    },
    {
      question: t('faq.items.privacy.question'),
      answer: t('faq.items.privacy.answer')
    },
    {
      question: t('faq.items.customize.question'),
      answer: t('faq.items.customize.answer')
    },
    {
      question: t('faq.items.languages.question'),
      answer: t('faq.items.languages.answer')
    }
  ]

  return faqItems.map(item => ({
    '@type': 'Question',
    'name': item.question,
    'acceptedAnswer': {
      '@type': 'Answer',
      'text': item.answer
    }
  }))
}

useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(generateStructuredData())
    }
  ]
})

// 浏览器商店链接
const CHROME_STORE_URL = 'https://chrome.google.com/webstore/detail/mhhcjilkgnegleamofnbeacpnblblkhn'
const EDGE_STORE_URL = 'https://microsoftedge.microsoft.com/addons/detail/ieedpolbpalhomefmggdickoicpoodab'

// 使用 ref 存储动态检测的浏览器商店链接
const browserStoreUrl = ref(CHROME_STORE_URL)

// 检测浏览器类型
const detectBrowser = () => {
  if (process.client) {
    const userAgent = navigator.userAgent.toLowerCase()

    // 检测 Microsoft Edge (基于 Chromium 的 Edge)
    if (userAgent.includes('edg/')) {
      browserStoreUrl.value = EDGE_STORE_URL
      return
    }

    // 检测 Chrome
    if (userAgent.includes('chrome/') && !userAgent.includes('edg/')) {
      browserStoreUrl.value = CHROME_STORE_URL
      return
    }

    // 默认使用 Chrome Store
    browserStoreUrl.value = CHROME_STORE_URL
  }
}

// 检测浏览器类型并返回相应的商店文本
const browserStoreText = computed(() => {
  if (process.client) { // 确保只在客户端运行
    const userAgent = navigator.userAgent.toLowerCase()
    const isEdge = userAgent.includes('edg/')
    const isChrome = userAgent.includes('chrome/') && !userAgent.includes('edg/')
    
    // 检测 Microsoft Edge
    if (isEdge) {
      return t('hero.edgeStore')
    }
    
    // 检测 Chrome
    if (isChrome) {
      return t('hero.chromeStore')
    }
    
    // 额外检测：如果存在 msLaunchUri 方法，也可能是 Edge
    if ('msLaunchUri' in navigator) {
      return t('hero.edgeStore')
    }
  }
  
  // 默认返回 Chrome Store 文本
  return t('hero.chromeStore')
})

// 使用翻译键数组
const features = [
  {
    id: 'form-filling',
    translationKey: 'formFilling',
    icon: ScanLine,
    demoComponent: FormDetectionDemo,
    reverse: false
  },
  {
    id: 'email',
    translationKey: 'email',
    icon: ToggleLeft,
    demoComponent: ModeSwitcherDemo,
    reverse: true
  },
  {
    id: 'bug-report',
    translationKey: 'bugReport',
    icon: Zap,
    demoComponent: AutoFillDemo,
    reverse: false
  },
  {
    id: 'ai-provider',
    translationKey: 'aiProvider',
    icon: Settings,
    demoComponent: AIProvidersDemo,
    reverse: true
  }
]

const ctaSection = ref<HTMLElement | null>(null)
const featuresSection = ref<HTMLElement | null>(null)
const chromeDemoContainer = ref<HTMLElement | null>(null)
const shouldStartDemoAnimation = ref(false)

const scrollToFeatures = () => {
  featuresSection.value?.scrollIntoView({ behavior: 'smooth' })
}

const openFaq = ref<number[]>([])
const toggleFaq = (index: number) => {
  const position = openFaq.value.indexOf(index)
  if (position > -1) {
    openFaq.value.splice(position, 1)
  } else {
    openFaq.value.push(index)
  }
}

// FAQ animation observer
const faqSection = ref<HTMLElement | null>(null)

onMounted(() => {
  // 默认展开第一个 FAQ
  openFaq.value = [0]

  // 检测浏览器类型
  detectBrowser()

  // FAQ Section observer
  useIntersectionObserver(faqSection, ([{ isIntersecting }]) => {
    if (isIntersecting && faqSection.value) {
      faqSection.value.classList.add('animate-feature')
    }
  })

  // Chrome Demo observer - start animation only when fully visible
  useIntersectionObserver(
    chromeDemoContainer,
    ([{ isIntersecting, intersectionRatio }]) => {
      // Only start animation when the demo is at least 80% visible
      if (isIntersecting && intersectionRatio >= 0.8 && !shouldStartDemoAnimation.value) {
        shouldStartDemoAnimation.value = true
      }
    },
    {
      threshold: 0.8 // Trigger when 80% of the element is visible
    }
  )
})

onUnmounted(() => {
})
</script>

<style scoped>
/* ==========================================
   HERO SECTION GRID
   ========================================== */

.hero-container {
  position: relative;
  overflow: hidden;
}

/* Hero Grid Container */
.hero-grid-container {
  position: absolute;
  inset: 0;
  pointer-events: none;
  overflow: hidden;
  /* Adjusted to cover small heading to CTA buttons, wider at main heading */
  mask-image: radial-gradient(ellipse 75% 50% at 50% 35%,
    black 15%,
    rgba(0, 0, 0, 0.6) 35%,
    rgba(0, 0, 0, 0.2) 55%,
    transparent 75%
  );
  -webkit-mask-image: radial-gradient(ellipse 75% 50% at 50% 35%,
    black 15%,
    rgba(0, 0, 0, 0.6) 35%,
    rgba(0, 0, 0, 0.2) 55%,
    transparent 75%
  );
}

/* Hero Grid Lines */
.hero-grid-lines {
  position: absolute;
  top: -80px;
  left: -80px;
  right: -80px;
  bottom: -80px;
  background-image:
    linear-gradient(to right, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 80px 80px;
  animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(80px, 80px);
  }
}

/* ==========================================
   ANIMATIONS
   ========================================== */

/* FAQ Items */
.faq-item {
  background: transparent;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  border-radius: 12px;
  padding: 0 1.5rem;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
}

.faq-item:hover {
  background: transparent;
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out forwards;
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-15px) translateX(5px);
  }
  50% {
    transform: translateY(-25px) translateX(-5px);
  }
  75% {
    transform: translateY(-15px) translateX(-10px);
  }
}

@keyframes float-reverse {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-20px) translateX(-8px);
  }
  50% {
    transform: translateY(-30px) translateX(8px);
  }
  75% {
    transform: translateY(-20px) translateX(12px);
  }
}

@keyframes float-delay-0 {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-15px) translateX(5px);
  }
  50% {
    transform: translateY(-25px) translateX(-5px);
  }
  75% {
    transform: translateY(-15px) translateX(-10px);
  }
}

@keyframes float-delay-500 {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-18px) translateX(7px);
  }
  50% {
    transform: translateY(-22px) translateX(-8px);
  }
  75% {
    transform: translateY(-20px) translateX(-12px);
  }
}

@keyframes float-delay-700 {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-12px) translateX(-6px);
  }
  50% {
    transform: translateY(-28px) translateX(6px);
  }
  75% {
    transform: translateY(-16px) translateX(10px);
  }
}

@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradient-x 15s ease infinite;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
  will-change: transform, opacity;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-reverse {
  animation: float-reverse 8s ease-in-out infinite;
}

/* ==========================================
   STACKED CARDS CONTAINER
   ========================================== */

.stacked-cards-container {
  position: relative;
  padding: 2rem 0;
}

@media (min-width: 1024px) {
  .stacked-cards-container {
    padding: 4rem 0;
    /* GSAP ScrollTrigger will handle the spacing */
  }
}
</style>
