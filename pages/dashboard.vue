<template>
  <div class="min-h-screen bg-gray-50/50">
    <ClientOnly>
      <!-- Header Background -->
      <div class="absolute top-0 left-0 right-0 h-[300px] bg-gradient-to-br from-blue-600 via-blue-500 to-cyan-500 -z-10"></div>
      
      <!-- Main Content -->
      <div class="container mx-auto px-4 pt-32 pb-12">
        <!-- User Profile Header -->
        <div class="flex flex-col md:flex-row items-center gap-6 mb-12 text-white">
          <div class="relative group">
            <div class="absolute -inset-1 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full blur opacity-25 group-hover:opacity-75 transition duration-1000 group-hover:duration-200"></div>
            <div class="relative w-24 h-24 rounded-full overflow-hidden border-4 border-white/20 shadow-xl">
              <img 
                v-if="user?.picture_url" 
                :src="user.picture_url" 
                :alt="user ? getDisplayName(user) : 'User avatar'" 
                class="w-full h-full object-cover"
              >
              <div v-else class="w-full h-full bg-white/10 backdrop-blur-md flex items-center justify-center">
                <span class="text-3xl font-bold text-white">{{ user ? getDisplayName(user)[0].toUpperCase() : 'U' }}</span>
              </div>
            </div>
          </div>
          
          <div class="text-center md:text-left">
            <h1 class="text-3xl font-bold mb-2">{{ user ? getDisplayName(user) : 'Welcome Back' }}</h1>
            <p class="text-blue-50 text-lg font-medium opacity-90">{{ user?.email }}</p>
          </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-5xl mx-auto">
          <!-- Subscription Plan Card -->
          <div class="bg-white/80 backdrop-blur-xl rounded-3xl border border-white/60 shadow-xl shadow-blue-900/5 p-8 relative overflow-hidden group hover:border-blue-200/60 transition-all duration-300">
            <div class="absolute top-0 right-0 w-32 h-32 bg-blue-500/5 rounded-full blur-3xl -mr-16 -mt-16 pointer-events-none"></div>
            
            <div class="flex justify-between items-start mb-6">
              <div>
                <h2 class="text-lg font-semibold text-gray-500 uppercase tracking-wider text-xs mb-1">{{ t('dashboard.currentPlan') }}</h2>
                <div class="text-3xl font-bold text-gray-900">Free Plan</div>
              </div>
              <span class="px-3 py-1 rounded-full bg-blue-50 text-blue-600 text-sm font-bold border border-blue-100 shadow-sm">
                Current
              </span>
            </div>
            
            <p class="text-gray-600 mb-8 leading-relaxed">
              You are currently on the Free Plan. Enjoy our core features to get started.
            </p>
            
            <button class="w-full py-3 px-4 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white rounded-xl font-medium transition-all shadow-lg shadow-blue-500/20 hover:shadow-blue-500/30 active:scale-[0.98]">
              Upgrade to Pro
            </button>
          </div>

          <!-- Usage Overview Card -->
          <div class="bg-white/80 backdrop-blur-xl rounded-3xl border border-white/60 shadow-xl shadow-blue-900/5 p-8 relative overflow-hidden group hover:border-cyan-200/60 transition-all duration-300">
            <div class="absolute bottom-0 left-0 w-32 h-32 bg-cyan-500/5 rounded-full blur-3xl -ml-16 -mb-16 pointer-events-none"></div>
            
            <div class="flex justify-between items-start mb-6">
              <div>
                <h2 class="text-lg font-semibold text-gray-500 uppercase tracking-wider text-xs mb-1">{{ t('dashboard.usageOverview') }}</h2>
                <div class="text-3xl font-bold text-gray-900">
                  {{ user?.credits !== undefined ? user.credits : 0 }} 
                  <span class="text-lg text-gray-400 font-normal">Credits</span>
                </div>
              </div>
              <div class="w-10 h-10 rounded-full bg-cyan-50 flex items-center justify-center text-cyan-600 border border-cyan-100">
                <Sparkles class="w-5 h-5" />
              </div>
            </div>

            <div class="space-y-4">
              <div class="flex justify-between text-sm font-medium">
                <span class="text-gray-600">Monthly Usage</span>
                <span class="text-gray-900">{{ user?.credits !== undefined ? Math.max(0, 10 - user.credits) : 0 }} / 10</span>
              </div>
              <div class="h-3 bg-gray-100 rounded-full overflow-hidden border border-gray-100">
                <div 
                  class="h-full bg-gradient-to-r from-blue-500 to-cyan-400 rounded-full transition-all duration-1000 ease-out"
                  :style="{ width: `${Math.min(100, Math.max(0, (10 - (user?.credits || 0)) / 10 * 100))}%` }"
                ></div>
              </div>
              <p class="text-sm text-gray-500 pt-2">
                Credits reset on the 1st of every month.
              </p>
            </div>
          </div>
        </div>
      </div>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { Circle, Settings, UserCircle, Sparkles } from 'lucide-vue-next'
import { useAuth } from '~/composables/useAuth'
import { useSeo } from '~/composables/useSeo'
import { inject, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'

// Use dashboard layout instead of default
definePageMeta({
  layout: 'dashboard'
})

const { t } = useI18n()
const { user, fetchUserData, getDisplayName } = useAuth()
const { setSeoMeta } = useSeo()
const userState = inject('userState') as {
  isLoggedIn: boolean
  credits: number
  userId: string | null
}

// 设置SEO元数据
setSeoMeta({ i18nKey: 'dashboard', robots: 'noindex,nofollow' })

const openExtensionSettings = () => {
  window.open('chrome-extension://mhhcjilkgnegleamofnbeacpnblblkhn/settings.html', '_blank')
}

onMounted(async () => {
  await fetchUserData()
})
</script>
