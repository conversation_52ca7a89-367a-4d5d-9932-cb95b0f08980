<template>
  <div class="min-h-screen flex items-center justify-center relative">
    <!-- Logo -->
    <div class="absolute top-6 left-6 md:top-8 md:left-8">
      <NuxtLink :to="localePath('/')" class="flex items-center gap-3 hover:opacity-80 transition-opacity">
        <img src="/logo/Fillify-Logo.svg" alt="Fillify Logo" class="h-8 w-auto" />
        <span class="text-xl font-semibold text-gray-900">
          Fillify
        </span>
      </NuxtLink>
    </div>

    <!-- Main Content -->
    <main class="w-full px-4 flex flex-col items-center pt-20 md:pt-0">
      <div class="max-w-md w-full">
        <!-- Sign in card -->
        <div class="bg-white/80 backdrop-blur-sm py-8 px-4 shadow-lg rounded-lg sm:px-10">
          <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900">{{ $t('signin.title') }}</h2>
            <p class="mt-2 text-sm text-gray-600">
              {{ $t('signin.subtitle') }}
            </p>
          </div>

          <!-- Email Login Section -->
          <div class="mb-6">
            <!-- Email Input -->
            <div v-if="!otpSent">
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('signin.email.label') }}
              </label>
              <input
                id="email"
                v-model="email"
                type="email"
                required
                :disabled="isLoading"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                :placeholder="$t('signin.email.placeholder')"
              />
              <button
                @click="sendOTP"
                :disabled="isLoading || !email || !isValidEmail"
                class="w-full mt-3 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span v-if="isLoading" class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {{ $t('signin.email.sending') }}
                </span>
                <span v-else>{{ $t('signin.email.continue') }}</span>
              </button>
            </div>

            <!-- OTP Input -->
            <div v-else>
              <div class="text-center mb-4">
                <p class="text-sm text-gray-600">
                  {{ $t('signin.otp.sentMessage') }}
                </p>
                <p class="text-sm font-medium text-gray-900">{{ email }}</p>
              </div>

              <label for="otp" class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('signin.otp.label') }}
              </label>
              <input
                id="otp"
                v-model="otp"
                type="text"
                required
                maxlength="6"
                :disabled="isLoading"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 text-center text-lg tracking-widest"
                :placeholder="$t('signin.otp.placeholder')"
              />

              <button
                @click="verifyOTP"
                :disabled="isLoading || !otp || otp.length !== 6"
                class="w-full mt-3 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span v-if="isLoading" class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {{ $t('signin.otp.verifying') }}
                </span>
                <span v-else>{{ $t('signin.otp.continue') }}</span>
              </button>

              <!-- Resend Code -->
              <div class="text-center mt-3">
                <button
                  @click="resendOTP"
                  :disabled="isResending || resendCooldown > 0"
                  class="text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="isResending" class="flex items-center justify-center">
                    <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-1"></div>
                    {{ $t('signin.otp.resend.resending') }}
                  </span>
                  <span v-else-if="resendCooldown > 0">
                    {{ $t('signin.otp.resend.cooldown', { seconds: resendCooldown }) }}
                  </span>
                  <span v-else>{{ $t('signin.otp.resend.action') }}</span>
                </button>
              </div>
            </div>

            <!-- Error message -->
            <div v-if="errorMessage" class="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-red-700">{{ errorMessage }}</p>
                </div>
              </div>
            </div>

          </div>

          <!-- Divider and Google Login (only show when not in OTP verification mode) -->
          <div v-if="!otpSent">
            <!-- Divider -->
            <div class="relative mb-6">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300" />
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white text-gray-500">{{ $t('signin.divider') }}</span>
              </div>
            </div>

            <!-- Google Sign In Button -->
            <div class="mb-6">
              <button
                @click="signInWithGoogle"
                :disabled="isGoogleLoading"
                class="w-full flex items-center justify-center gap-3 border border-gray-300 rounded-md py-3 px-4 text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors whitespace-nowrap min-h-[46px] sm:text-base text-sm"
              >
                <span v-if="isGoogleLoading" class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-700 mr-2"></div>
                  {{ $t('signin.google.loading') }}
                </span>
                <span v-else class="flex items-center">
                  <svg class="w-5 h-5 flex-shrink-0" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  <span class="ml-2 truncate">{{ $t('signin.google.continue') }}</span>
                </span>
              </button>
            </div>
          </div>

          <!-- Features -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-sm font-medium text-gray-500 mb-4">{{ $t('signin.features.title') }}</h3>
            <ul class="space-y-4">
              <li class="flex items-center text-sm text-gray-600">
                <Sparkles class="h-5 w-5 text-blue-500 mr-3" />
                {{ $t('signin.features.list.autoFill') }}
              </li>
              <li class="flex items-center text-sm text-gray-600">
                <Wand class="h-5 w-5 text-purple-500 mr-3" />
                {{ $t('signin.features.list.api') }}
              </li>
              <li class="flex items-center text-sm text-gray-600">
                <Rocket class="h-5 w-5 text-orange-500 mr-3" />
                {{ $t('signin.features.list.early') }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Terms and Privacy -->
        <div class="mt-6 text-center text-xs text-gray-500">
          <span class="block">{{ $t('signin.terms.prefix') }}</span>
          <span class="mt-1 inline-block">
            <NuxtLink to="/terms" class="text-blue-600 hover:text-blue-800">
              {{ $t('signin.terms.termsOfService') }}
            </NuxtLink>
            <span class="mx-1">{{ $t('signin.terms.and') }}</span>
            <NuxtLink to="/privacy" class="text-blue-600 hover:text-blue-800">
              {{ $t('signin.terms.privacyPolicy') }}
            </NuxtLink>
          </span>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, inject, watch, computed } from 'vue'
import { cookieUtil } from '~/utils/cookie'
import { Sparkles, Wand, Rocket } from 'lucide-vue-next'
import { useAuth } from '~/composables/useAuth'
import { useSupabase } from '~/composables/useSupabase'

// Type definitions
interface UserState {
  isLoggedIn: boolean
  credits: number
  userId: string | null
}

const { locale, t } = useI18n()
const localePath = useLocalePath()
const { user, isLoggedIn, fetchUserData, verifyEmailOTP: authVerifyOTP, signInWithGoogle: authSignInWithGoogle } = useAuth()
const supabase = useSupabase()
const isLoading = ref(false)

// Get type-safe userState
const userState = inject<UserState>('userState')

// Email OTP login related state
const email = ref('')
const otp = ref('')
const otpSent = ref(false)
const errorMessage = ref('')
const resendCooldown = ref(0)
const isResending = ref(false)
const isGoogleLoading = ref(false)

// Email format validation
const isValidEmail = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email.value)
})

// Watch login status
watch(isLoggedIn, (newValue) => {
  if (newValue) {
    const currentLang = locale.value
    const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
    navigateTo(dashboardPath)
  }
}, { immediate: true })

// Send OTP
const sendOTP = async () => {
  if (!email.value || !isValidEmail.value) {
    errorMessage.value = t('signin.error.invalidEmail')
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const response = await $fetch('/api/auth/send-otp', {
      method: 'POST',
      body: {
        email: email.value
      }
    })

    if (response.success) {
      otpSent.value = true
      startResendCooldown()
    }
  } catch (error: any) {
    console.error('Send OTP error:', error)
    // Provide more specific error messages
    if (error.status === 400) {
      errorMessage.value = t('signin.error.invalidEmail')
    } else if (error.status === 429) {
      errorMessage.value = t('signin.error.tooManyRequests')
    } else if (error.status === 500) {
      errorMessage.value = t('signin.error.serverError')
    } else {
      errorMessage.value = error.data?.message || t('signin.error.sendOTP')
    }
  } finally {
    isLoading.value = false
  }
}

// Resend OTP (separate function, doesn't affect main button loading state)
const resendOTP = async () => {
  if (!email.value || !isValidEmail.value) {
    errorMessage.value = t('signin.error.invalidEmail')
    return
  }

  isResending.value = true
  errorMessage.value = ''

  try {
    const response = await $fetch('/api/auth/send-otp', {
      method: 'POST',
      body: {
        email: email.value
      }
    })

    if (response.success) {
      startResendCooldown()
    }
  } catch (error: any) {
    console.error('Resend OTP error:', error)
    // Provide more specific error messages
    if (error.status === 400) {
      errorMessage.value = t('signin.error.invalidEmail')
    } else if (error.status === 429) {
      errorMessage.value = t('signin.error.tooManyRequests')
    } else if (error.status === 500) {
      errorMessage.value = t('signin.error.serverError')
    } else {
      errorMessage.value = error.data?.message || t('signin.error.sendOTP')
    }
  } finally {
    isResending.value = false
  }
}

// Verify OTP and login
const verifyOTP = async () => {
  if (!otp.value || otp.value.length !== 6) {
    errorMessage.value = t('signin.error.invalidOTP')
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const response = await authVerifyOTP(email.value, otp.value)

    if (response.success && response.user) {
      // Set cookie for auth
      cookieUtil.setCookie('xToken', response.user.id, {
        expires: 7,
        path: '/'
      })

      // Wait for state update to complete
      await nextTick()
      // Force refresh user data
      await fetchUserData()

      // 导航到仪表板
      try {
        const currentLang = locale.value
        const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
        await navigateTo(dashboardPath, { replace: true })
      } catch (navError) {
        console.error('Navigation error:', navError)
        const localePath = useLocalePath()
        window.location.href = localePath('/dashboard')
      }
    }
  } catch (error: any) {
    console.error('Verify OTP error:', error)
    // Provide more specific error messages for OTP verification
    if (error.status === 400) {
      const message = error.data?.message || ''
      if (message.includes('Invalid or expired')) {
        errorMessage.value = t('signin.error.expiredOTP')
      } else if (message.includes('Invalid verification code')) {
        errorMessage.value = t('signin.error.wrongOTP')
      } else {
        errorMessage.value = t('signin.error.invalidOTP')
      }
    } else if (error.status === 429) {
      errorMessage.value = t('signin.error.tooManyAttempts')
    } else if (error.status === 500) {
      errorMessage.value = t('signin.error.serverError')
    } else {
      errorMessage.value = error.data?.message || t('signin.error.verifyOTP')
    }
  } finally {
    isLoading.value = false
  }
}

// Start resend cooldown
const startResendCooldown = () => {
  resendCooldown.value = 60
  const timer = setInterval(() => {
    resendCooldown.value--
    if (resendCooldown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// Sign in with Google using Supabase
const signInWithGoogle = async () => {
  isGoogleLoading.value = true
  errorMessage.value = ''

  try {
    const response = await authSignInWithGoogle()
    
    if (response?.error) {
      throw new Error(response.error.message)
    }
    
    // If successful, the user will be redirected automatically
    // by the Supabase auth state change listener in useAuth
  } catch (error: any) {
    console.error('Google login error:', error)
    errorMessage.value = error.message || t('signin.error.googleLogin')
  } finally {
    isGoogleLoading.value = false
  }
}

// SEO
import { useSeo } from '~/composables/useSeo'
const { setSeoMeta } = useSeo()

// 设置SEO元数据
setSeoMeta({ i18nKey: 'signin', robots: 'noindex,nofollow' })
</script>
