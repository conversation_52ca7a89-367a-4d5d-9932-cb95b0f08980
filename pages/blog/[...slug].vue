<template>
  <div class="min-h-screen relative">

    <main class="relative">
      <article class="container mx-auto px-4 py-24 md:py-32 max-w-4xl">
        <template v-if="doc">
          <!-- Navigation -->
          <nav class="mb-12 animate-slide-up">
            <NuxtLink 
              :to="localePath('/blog')" 
              class="inline-flex items-center gap-2 text-gray-500 hover:text-gray-900 transition-colors group font-medium"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left group-hover:-translate-x-1 transition-transform">
                <path d="M12 19l-7-7 7-7"/>
                <path d="M19 12H5"/>
              </svg>
              {{ t('blog.article.backToBlog') }}
            </NuxtLink>
          </nav>

          <!-- Article Header -->
          <header class="mb-16 animate-slide-up [animation-delay:200ms] text-center">
            <!-- Tags -->
            <div v-if="Array.isArray(doc.tags) && doc.tags.length" class="flex flex-wrap gap-2 mb-8 justify-center">
              <span 
                v-for="tag in doc.tags" 
                :key="tag" 
                class="px-4 py-1.5 text-sm font-medium bg-gray-100 text-gray-600 rounded-full border border-gray-200/50"
              >
                {{ tag }}
              </span>
            </div>

            <!-- Title -->
            <h1 class="text-4xl md:text-6xl font-bold tracking-tight text-gray-900 mb-8 leading-[1.1]">
              {{ doc.title }}
            </h1>

            <!-- Description -->
            <p v-if="doc.description" class="text-xl md:text-2xl text-gray-500 leading-relaxed mb-12 max-w-3xl mx-auto font-light">
              {{ doc.description }}
            </p>

            <!-- Meta Info -->
            <div class="flex items-center justify-center gap-8 pt-8 border-t border-gray-100">
              <div class="flex items-center gap-2 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar">
                  <rect width="18" height="18" x="3" y="4" rx="2" ry="2"/>
                  <line x1="16" x2="16" y1="2" y2="6"/>
                  <line x1="8" x2="8" y1="2" y2="6"/>
                  <line x1="3" x2="21" y1="10" y2="10"/>
                </svg>
                <span class="font-medium">{{ formatDate(doc.date) }}</span>
              </div>
              
              <div class="flex items-center gap-2 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock">
                  <circle cx="12" cy="12" r="10"/>
                  <polyline points="12,6 12,12 16,14"/>
                </svg>
                <span class="font-medium">{{ getReadTime(doc.body) }} {{ t('blog.list.minRead') }}</span>
              </div>
            </div>
          </header>

          <!-- Article Content -->
          <div class="animate-slide-up [animation-delay:400ms]">
            <!-- Content Container -->
            <div class="bg-white/60 backdrop-blur-xl rounded-3xl border border-gray-200/50 p-8 md:p-16 shadow-2xl shadow-gray-200/50">
              <div class="prose prose-lg prose-gray max-w-none prose-headings:font-bold prose-headings:tracking-tight prose-p:leading-relaxed prose-p:text-gray-600 prose-a:text-gray-900 prose-a:no-underline hover:prose-a:underline prose-strong:text-gray-900 prose-code:text-gray-900 prose-code:bg-gray-100 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-blockquote:border-l-gray-300 prose-blockquote:bg-gray-50 prose-blockquote:pl-6 prose-blockquote:py-4 prose-blockquote:rounded-r prose-blockquote:text-gray-500 prose-ul:list-disc prose-ol:list-decimal prose-li:marker:text-gray-400 prose-img:rounded-2xl prose-img:shadow-lg">
                <ContentRenderer :value="doc" />
              </div>
            </div>
          </div>

          <!-- Article Footer -->
          <footer class="mt-16 animate-slide-up [animation-delay:600ms]">
            <div class="bg-gray-900 rounded-3xl p-12 text-center shadow-2xl shadow-gray-900/20 relative overflow-hidden">
              <!-- Background effects -->
              <div class="absolute inset-0 bg-[url('https://grainy-gradients.vercel.app/noise.svg')] opacity-10 mix-blend-soft-light"></div>
              <div class="absolute top-0 right-0 w-[300px] h-[300px] bg-blue-500/20 rounded-full blur-3xl -translate-y-1/2 translate-x-1/4"></div>
              
              <div class="relative z-10">
                <h3 class="text-2xl md:text-3xl font-bold text-white mb-4">{{ t('blog.article.thanksTitle') }}</h3>
                <p class="text-gray-400 mb-8 max-w-xl mx-auto">{{ t('blog.article.thanksDescription') }}</p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="https://chrome.google.com/webstore/detail/mhhcjilkgnegleamofnbeacpnblblkhn"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="inline-flex items-center justify-center px-8 py-4 text-gray-900 bg-white rounded-xl hover:bg-gray-50 transition-all shadow-lg hover:shadow-xl hover:-translate-y-1 font-semibold group"
                  >
                    {{ t('blog.article.tryFillify') }}
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link ml-2 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform">
                      <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                      <polyline points="15,3 21,3 21,9"/>
                      <line x1="10" x2="21" y1="14" y2="3"/>
                    </svg>
                  </a>
                  <NuxtLink
                    :to="localePath('/blog')"
                    class="inline-flex items-center justify-center px-8 py-4 text-white bg-white/10 backdrop-blur-sm rounded-xl hover:bg-white/20 transition-all border border-white/10 font-semibold group"
                  >
                    {{ t('blog.article.moreArticles') }}
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right ml-2 group-hover:translate-x-0.5 transition-transform">
                      <path d="M5 12h14"/>
                      <path d="M12 5l7 7-7 7"/>
                    </svg>
                  </NuxtLink>
                </div>
              </div>
            </div>
          </footer>
        </template>
        
        <!-- Not Found State -->
        <div v-else class="text-center py-32">
          <div class="max-w-md mx-auto">
            <div class="w-24 h-24 mx-auto mb-6 rounded-full bg-gray-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-x text-gray-400">
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="9.5" x2="14.5" y1="12.5" y2="17.5"/>
                <line x1="14.5" x2="9.5" y1="12.5" y2="17.5"/>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">{{ t('blog.article.notFoundTitle') }}</h3>
            <p class="text-gray-500 mb-8">{{ t('blog.article.notFoundDescription') }}</p>
            <NuxtLink
              :to="localePath('/blog')"
              class="inline-flex items-center justify-center px-8 py-4 text-white bg-gray-900 rounded-xl hover:bg-gray-800 transition-colors font-semibold"
            >
              {{ t('blog.article.backToBlogBtn') }}
            </NuxtLink>
          </div>
        </div>
      </article>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useBlogUtils } from '~/composables/useBlogUtils'
import { useBlogStructuredData } from '~/composables/useBlogStructuredData'

const route = useRoute()
const localePath = useLocalePath()
const switchLocalePath = useSwitchLocalePath()
const { t, locale, locales, defaultLocale } = useI18n()
const runtimeConfig = useRuntimeConfig()
const { formatDate, getReadTime, getWordCount } = useBlogUtils()
const { extractFAQSchema, extractHowToSchema } = useBlogStructuredData()

const siteUrl = String(runtimeConfig.public?.publicUrl || runtimeConfig.public?.siteUrl || 'https://fillify.tech').replace(/\/$/, '')

const resolveLanguageTag = (entry: any, fallback: string) => {
  if (!entry) return fallback
  return entry.language || entry.iso || entry.code || fallback
}

const resolveHrefLang = (entry: any, fallback: string) => {
  if (!entry) return fallback
  return entry.code || entry.language || entry.iso || fallback
}

const toAbsolute = (path?: string) => {
  if (!path) return `${siteUrl}/`
  const normalized = path.startsWith('/') ? path : `/${path}`
  if (normalized === '/') {
    return `${siteUrl}/`
  }
  return `${siteUrl}${normalized}`
}

const contentPath = computed(() => {
  const slug = route.params.slug
  const parts = Array.isArray(slug) ? slug : [String(slug)]
  return `/blog/${parts.join('/')}`
})

const { data: doc } = await useAsyncData(
  'blog-doc-' + (Array.isArray(route.params.slug) ? route.params.slug.join('/') : String(route.params.slug || '')),
  () => queryContent(contentPath.value).findOne(),
  { watch: [contentPath] }
)

// 为博客文章设置动态SEO
watch(doc, (newDoc) => {
  if (newDoc) {
    const canonicalPath = route.path || contentPath.value
    const canonicalUrl = toAbsolute(canonicalPath)

    const localeEntries = Array.isArray(locales.value) ? locales.value : []
    const currentLocaleEntry = localeEntries.find((entry: any) => (typeof entry === 'string' ? entry === locale.value : entry.code === locale.value))
    const ogLocale = resolveLanguageTag(currentLocaleEntry, 'en-US')
    const ogLocaleTag = ogLocale.replace('-', '_')

    const alternateLinks: { rel: string; hreflang: string; href: string; key: string }[] = []

    const defaultLocaleCode = (defaultLocale as any)?.value?.code || (defaultLocale as any)?.value || (typeof defaultLocale === 'string' ? defaultLocale : null)
    if (defaultLocaleCode) {
      const defaultPath = switchLocalePath(defaultLocaleCode)
      if (defaultPath) {
        alternateLinks.push({
          rel: 'alternate',
          hreflang: 'x-default',
          href: toAbsolute(defaultPath),
          key: 'alternate-x-default'
        })
      }
    }

    localeEntries.forEach((entry: any) => {
      const code = typeof entry === 'string' ? entry : entry.code
      if (!code) {
        return
      }

      const hrefLang = resolveHrefLang(entry, code)
      const localizedPath = code === locale.value ? canonicalPath : switchLocalePath(code)

      if (!localizedPath) {
        return
      }

      alternateLinks.push({
        rel: 'alternate',
        hreflang: hrefLang,
        href: toAbsolute(localizedPath),
        key: `alternate-${hrefLang}`
      })
    })

    const alternateLocaleMeta = localeEntries
      .filter((entry: any) => (typeof entry === 'string' ? entry !== locale.value : entry.code !== locale.value))
      .map((entry: any) => resolveLanguageTag(entry, ''))
      .map((lang: string) => lang && lang.replace('-', '_'))
      .filter(Boolean)

    const blogIndexPath = localePath('/blog') || '/blog'
    const breadcrumbs = [
      { name: t('nav.home'), url: toAbsolute('/') },
      { name: t('blog.hero.title'), url: toAbsolute(blogIndexPath) },
      { name: newDoc.title, url: canonicalUrl }
    ]

    const articleSchema = {
      '@context': 'https://schema.org',
      '@type': 'Article',
      headline: newDoc.title,
      description: newDoc.description || `Read Fillify blog article: ${newDoc.title}`,
      image: `${siteUrl}/og/og-image.webp`,
      author: {
        '@type': 'Organization',
        name: newDoc.author || 'Fillify Team',
        url: siteUrl
      },
      publisher: {
        '@type': 'Organization',
        name: 'Fillify',
        logo: {
          '@type': 'ImageObject',
          url: `${siteUrl}/logo/Fillify-Logo.svg`
        }
      },
      datePublished: newDoc.date,
      dateModified: newDoc.updated || newDoc.updatedAt || newDoc.date,
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': canonicalUrl
      },
      keywords: (newDoc.tags || []).join(', '),
      articleSection: 'Technology',
      wordCount: getWordCount(newDoc.body),
      timeRequired: `PT${newDoc.readTime || getReadTime(newDoc.body)}M`,
      inLanguage: ogLocale,
      about: {
        '@type': 'Thing',
        name: 'AI Form Filling',
        description: 'Artificial Intelligence powered form automation and productivity tools'
      },
      // Enhanced GEO metadata
      isAccessibleForFree: true,
      educationalUse: 'Tutorial',
      learningResourceType: 'Guide'
    }

    // Build structured data scripts
    const structuredDataScripts: any[] = [
      {
        type: 'application/ld+json',
        innerHTML: JSON.stringify(articleSchema)
      },
      {
        type: 'application/ld+json',
        innerHTML: JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'BreadcrumbList',
          itemListElement: breadcrumbs.map((crumb, index) => ({
            '@type': 'ListItem',
            position: index + 1,
            name: crumb.name,
            item: crumb.url
          }))
        })
      }
    ]

    // Extract FAQ from article content for GEO optimization
    const faqSchema = extractFAQSchema(newDoc.body)
    if (faqSchema && faqSchema.mainEntity && faqSchema.mainEntity.length > 0) {
      structuredDataScripts.push({
        type: 'application/ld+json',
        innerHTML: JSON.stringify(faqSchema)
      })
    }

    // Extract How-To steps from article content for GEO optimization
    const howToSchema = extractHowToSchema(
      newDoc.body,
      newDoc.title || '',
      newDoc.description
    )
    if (howToSchema && howToSchema.step && howToSchema.step.length > 0) {
      structuredDataScripts.push({
        type: 'application/ld+json',
        innerHTML: JSON.stringify(howToSchema)
      })
    }

    useHead({
      title: `${newDoc.title} - Fillify Blog`,
      link: [
        {
          rel: 'canonical',
          href: canonicalUrl,
          key: 'canonical'
        },
        ...alternateLinks
      ],
      meta: [
        { name: 'description', content: newDoc.description || `Read Fillify blog article: ${newDoc.title}`, key: 'description' },
        { name: 'keywords', content: (newDoc.tags || []).join(', ') + ', Fillify, AI, form filling, automation, productivity, Chrome extension', key: 'keywords' },
        { property: 'og:title', content: `${newDoc.title} - Fillify Blog`, key: 'og-title' },
        { property: 'og:description', content: newDoc.description || `Read Fillify blog article: ${newDoc.title}`, key: 'og-description' },
        { property: 'og:type', content: 'article', key: 'og-type' },
        { property: 'og:url', content: canonicalUrl, key: 'og-url' },
        { property: 'og:image', content: `${siteUrl}/og/og-image.webp`, key: 'og-image-webp' },
        { property: 'og:image:alt', content: 'Fillify - AI-Powered Form Automation Platform', key: 'og-image-alt' },
        { property: 'og:image', content: `${siteUrl}/og/og-image.png`, key: 'og-image-png' },
        { property: 'og:locale', content: ogLocaleTag, key: 'og-locale' },
        ...alternateLocaleMeta.map((lang: string, index: number) => ({
          property: 'og:locale:alternate',
          content: lang,
          key: `og-locale-alt-${index}`
        })),
        { property: 'article:published_time', content: newDoc.date, key: 'article-published' },
        { property: 'article:modified_time', content: newDoc.updatedAt || newDoc.date, key: 'article-modified' },
        { property: 'article:author', content: 'Fillify Team', key: 'article-author' },
        { property: 'article:section', content: 'Technology', key: 'article-section' },
        { property: 'article:tag', content: (newDoc.tags || []).join(', '), key: 'article-tag' },
        { name: 'twitter:card', content: 'summary_large_image', key: 'twitter-card' },
        { name: 'twitter:title', content: `${newDoc.title} - Fillify Blog`, key: 'twitter-title' },
        { name: 'twitter:description', content: newDoc.description || `Read Fillify blog article: ${newDoc.title}`, key: 'twitter-description' },
        { name: 'twitter:image', content: `${siteUrl}/og/og-image.png`, key: 'twitter-image' },
        { name: 'twitter:image:alt', content: 'Fillify - AI-Powered Form Automation Platform', key: 'twitter-image-alt' },
        { name: 'twitter:url', content: canonicalUrl, key: 'twitter-url' }
      ],
      script: structuredDataScripts
    })
  }
}, { immediate: true })
</script>

<style scoped>
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out forwards;
  opacity: 0;
}

/* 为长内容添加行限制 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
