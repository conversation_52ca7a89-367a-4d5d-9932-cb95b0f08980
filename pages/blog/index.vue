<template>
  <div class="min-h-screen relative">

    <main class="relative">
      <!-- Hero Section -->
      <section class="container mx-auto px-4 py-24 md:py-32 relative">
        <div class="max-w-4xl mx-auto text-center space-y-8 relative">
          <!-- Badge -->
          <div class="inline-flex animate-slide-up justify-center">
            <div class="flex items-center gap-2 px-4 py-2 text-sm rounded-full border border-gray-200 bg-white/80 text-gray-600 font-medium backdrop-blur-md shadow-sm">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-newspaper">
                <path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2"/>
                <path d="M18 14h-8"/>
                <path d="M15 18h-5"/>
                <path d="M10 6h8v4h-8V6Z"/>
              </svg>
              {{ t('blog.hero.badge') }}
            </div>
          </div>

          <!-- Main Heading -->
          <h1 class="text-5xl md:text-7xl font-bold tracking-tight text-gray-900 animate-slide-up [animation-delay:200ms] leading-[1.1]">
            {{ t('blog.hero.title') }}
          </h1>

          <!-- Description -->
          <p class="text-xl md:text-2xl text-gray-600 max-w-2xl mx-auto leading-relaxed animate-slide-up [animation-delay:400ms] font-light">
            {{ t('blog.hero.subtitle') }}
          </p>
        </div>
      </section>

      <!-- Blog Posts Section -->
      <section class="container mx-auto px-4 pb-24 md:pb-32 relative">
        <ContentList :query="{ where: [{ _dir: { $eq: 'blog' } }], sort: [{ date: -1 }] }">
          <template #default="{ list }">
            <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3 max-w-7xl mx-auto">
              <article
                v-for="(post, index) in list"
                :key="post._path || `post-${index}`"
                class="group rounded-3xl border border-gray-200/50 hover:border-gray-300/80 transition-all duration-500 p-8 bg-white/60 backdrop-blur-xl hover:shadow-2xl hover:shadow-gray-200/50 animate-slide-up flex flex-col h-full"
                :style="`animation-delay: ${index * 100 + 600}ms`"
              >
                <NuxtLink v-if="post._path" :to="localePath(post._path)" class="flex flex-col h-full">
                  <!-- Tags -->
                  <div v-if="Array.isArray(post.tags) && post.tags.length" class="flex flex-wrap gap-2 mb-6">
                    <span 
                      v-for="tag in post.tags.slice(0, 2)" 
                      :key="tag" 
                      class="px-3 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full border border-gray-200/50"
                    >
                      {{ tag }}
                    </span>
                    <span v-if="post.tags.length > 2" class="px-3 py-1 text-xs text-gray-400 rounded-full bg-gray-50 border border-gray-100">
                      +{{ post.tags.length - 2 }}
                    </span>
                  </div>

                  <!-- Title -->
                  <h2 class="text-2xl font-bold text-gray-900 group-hover:text-gray-600 transition-colors duration-300 mb-4 line-clamp-2 leading-tight">
                    {{ post.title }}
                  </h2>

                  <!-- Description -->
                  <p v-if="post.description" class="text-gray-500 leading-relaxed mb-8 flex-grow line-clamp-3 font-light">
                    {{ post.description }}
                  </p>

                  <!-- Meta Info -->
                  <div class="flex items-center justify-between pt-6 border-t border-gray-100 mt-auto">
                    <div class="flex items-center gap-2 text-sm text-gray-400 font-medium">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar">
                        <rect width="18" height="18" x="3" y="4" rx="2" ry="2"/>
                        <line x1="16" x2="16" y1="2" y2="6"/>
                        <line x1="8" x2="8" y1="2" y2="6"/>
                        <line x1="3" x2="21" y1="10" y2="10"/>
                      </svg>
                      {{ formatDate(post.date) }}
                    </div>
                    
                    <div class="flex items-center gap-2 text-gray-900 text-sm font-semibold group-hover:translate-x-1 transition-transform duration-300">
                      {{ t('blog.list.readMore') }}
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right">
                        <path d="M5 12h14"/>
                        <path d="M12 5l7 7-7 7"/>
                      </svg>
                    </div>
                  </div>
                </NuxtLink>
              </article>
            </div>
          </template>

          <template #not-found>
            <div class="text-center py-32">
              <div class="max-w-md mx-auto">
                <div class="w-20 h-20 mx-auto mb-6 rounded-full bg-gray-100 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text text-gray-400">
                    <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                    <polyline points="14,2 14,8 20,8"/>
                    <line x1="16" x2="8" y1="13" y2="13"/>
                    <line x1="16" x2="8" y1="17" y2="17"/>
                    <line x1="10" x2="8" y1="9" y2="9"/>
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">{{ t('blog.list.noPostsTitle') }}</h3>
                <p class="text-gray-500">{{ t('blog.list.noPostsDescription') }}</p>
              </div>
            </div>
          </template>
        </ContentList>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useSeo } from '~/composables/useSeo'
import { useBlogUtils } from '~/composables/useBlogUtils'

const localePath = useLocalePath()
const { setSeoMeta } = useSeo()
const { t } = useI18n()
const { formatDate } = useBlogUtils()

// 设置SEO元数据
setSeoMeta({ i18nKey: 'blog' })
</script>

<style scoped>
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out forwards;
  opacity: 0;
}

/* 为长内容添加行限制 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
