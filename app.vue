<template>
  <NuxtLayout>
    <NuxtPage />
  </NuxtLayout>
</template>

<script setup lang="ts">
import { onMounted, reactive, provide } from 'vue'
import { cookieUtil } from '~/utils/cookie'
import { useAuth } from '~/composables/useAuth'

// 类型定义
interface UserState {
  isLoggedIn: boolean
  credits: number
  userId: string | null
}

interface User {
  id: string
  email: string
  credits?: number
  [key: string]: any
}

const { locale, setLocale, t } = useI18n()
const localePath = useLocalePath()
const { user, fetchUserData } = useAuth() // 使用 useAuth 的状态

const userState = reactive<UserState>({
  isLoggedIn: false,
  credits: 3,
  userId: null
})

provide('userState', userState)

// New functions for local storage
function saveUserState() {
  localStorage.setItem('userState', JSON.stringify(userState))
}

function loadUserState() {
  const savedState = localStorage.getItem('userState')
  if (savedState) {
    const parsedState = JSON.parse(savedState)
    Object.assign(userState, parsedState)
    return true
  }
  return false
}

function clearUserState() {
  localStorage.removeItem('userState')
}

onMounted(() => {
  // 先尝试从localStorage加载状态
  const hasLocalState = loadUserState()
  
  // 检查cookie中是否存在xToken
  const xToken = cookieUtil.getCookie('xToken')
  
  // 只有在有xToken且本地状态无效时才获取用户数据
  if (xToken && !hasLocalState) {
    fetchUserData()
  }

  // 监听用户状态更新事件
  window.addEventListener('userStateUpdate', (event: any) => {
    const newUserState = event.detail
    Object.assign(userState, newUserState)
  })
})

useHead({
  script: [
    {// supabase
      src: 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2',
      onload: () => { window.dispatchEvent(new Event('supabase-loaded')); }
    },
  ],
  htmlAttrs: {
    lang: locale.value
  }
})
</script>
