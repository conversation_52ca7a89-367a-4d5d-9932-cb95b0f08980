import { defineEventHandler } from 'h3'

export default defineEventHandler(async (event) => {
  const { serverQueryContent } = await import('#content/server')

  const posts = await serverQueryContent(event)
    .only(['_path', 'updatedAt', 'date', 'published'])
    .find()

  return posts
    .filter((post: any) => post?._path?.startsWith('/blog/') && post?.published !== false)
    .map((post: any) => {
      const lastmod = post.updatedAt || post.date
      return {
        loc: post._path,
        lastmod: lastmod ? new Date(lastmod).toISOString() : undefined,
        changefreq: 'weekly' as const,
        priority: 0.7 as const
      }
    })
})
