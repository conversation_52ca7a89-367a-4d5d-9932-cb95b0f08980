import { defineEventHandler } from 'h3'
import { useSupabaseServer } from '~/composables/useSupabase'

export default defineEventHandler(async (event) => {
  try {
    const config = useRuntimeConfig()
    const supabase = useSupabaseServer()

    // 测试连接和配置
    const testResults = {
      config: {
        supabaseUrl: config.supabaseUrl,
        hasServiceRoleKey: !!config.supabaseServiceRoleKey,
        hasAnonKey: !!config.public.supabaseAnonKey,
      },
      connection: null,
      auth: null,
      tables: null
    }

    try {
      // 测试数据库连接
      const { data: tables, error: tablesError } = await supabase
        .from('users')
        .select('count(*)', { count: 'exact', head: true })

      if (tablesError) {
        testResults.connection = { error: tablesError.message }
      } else {
        testResults.connection = { success: true, userCount: tables }
      }
    } catch (err: any) {
      testResults.connection = { error: err.message }
    }

    try {
      // 测试邮箱认证（不实际发送）
      const { error: authError } = await supabase.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'test123',
        email_confirm: true
      })

      // 立即删除测试用户
      if (!authError) {
        await supabase.auth.admin.deleteUser('test-user-id')
      }

      testResults.auth = authError ? { error: authError.message } : { success: true }
    } catch (err: any) {
      testResults.auth = { error: err.message }
    }

    return {
      success: true,
      results: testResults,
      timestamp: new Date().toISOString()
    }

  } catch (error: any) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }
  }
})