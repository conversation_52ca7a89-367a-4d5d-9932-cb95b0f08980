<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Fillify Popup - Chrome Browser Demo</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f0f0f0;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    /* Chrome Browser Frame - Figma Style */
    .chrome-browser {
      width: 100%;
      max-width: 1440px;
      height: 95vh;
      margin: 2.5vh auto;
      background: white;
      box-shadow: 0px 16px 48px rgba(0, 0, 0, 0.50);
      overflow: hidden;
      border-radius: 10px;
      outline: 0.50px #A6A6A6 solid;
      outline-offset: -0.50px;
      display: flex;
      flex-direction: column;
      position: relative;
    }

    /* Chrome Top Bar - Figma Style */
    .chrome-topbar {
      height: 42px;
      background: #DFE1E5;
      position: relative;
      display: flex;
      align-items: center;
      padding: 0 21px;
    }

    .chrome-dots {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .chrome-dot {
      width: 12px;
      height: 12px;
      border-radius: 6px;
    }

    .chrome-dot.red { background: #EC6B5E; }
    .chrome-dot.yellow { background: #F4BF4F; }
    .chrome-dot.green { background: #61C453; }

    /* Chrome Tab Bar - Figma Style */
    .chrome-tabbar {
      position: absolute;
      left: 87px;
      top: 0;
      height: 42px;
      display: flex;
      align-items: flex-end;
      padding-bottom: 0;
      overflow: visible;
    }

    .chrome-tab {
      width: 240px;
      height: 34px;
      position: relative;
      background: white;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      display: flex;
      align-items: center;
      padding: 0 10px;
      overflow: visible;
    }

    .chrome-tab-fade {
      width: 44px;
      height: 34px;
      position: absolute;
      right: 0;
      top: 0;
      background: linear-gradient(270deg, white 59%, rgba(255, 255, 255, 0) 100%);
      pointer-events: none;
      border-top-right-radius: 8px;
    }

    .chrome-tab-close {
      width: 8px;
      height: 8px;
      position: absolute;
      right: 10px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .chrome-tab-title-wrapper {
      flex: 1;
      overflow: hidden;
    }

    .chrome-tab-title {
      color: #3D4043;
      font-size: 12px;
      font-family: Roboto, sans-serif;
      font-weight: 600;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .chrome-new-tab-btn {
      position: absolute;
      left: 350px;
      width: 12px;
      height: 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .chrome-window-controls {
      position: absolute;
      right: 21px;
      width: 10px;
      height: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* Chrome Address Bar - Figma Style */
    .chrome-addressbar {
      height: 36px;
      background: white;
      display: flex;
      align-items: center;
      padding: 0 16px;
      gap: 16px;
      position: relative;
    }

    .chrome-back-btn,
    .chrome-forward-btn,
    .chrome-refresh-btn {
      width: 14px;
      height: 14px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .chrome-url-bar {
      flex: 1;
      height: 28px;
      background: #F1F3F4;
      border-radius: 9999px;
      display: flex;
      align-items: center;
      padding: 0 14px;
      gap: 8px;
      position: relative;
    }

    .chrome-lock-icon {
      width: 8px;
      height: 11px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .chrome-url-text-wrapper {
      flex: 1;
      overflow: hidden;
    }

    .chrome-url {
      color: #606367;
      font-size: 14px;
      font-family: Roboto, sans-serif;
      font-weight: 400;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .chrome-star-icon,
    .chrome-share-icon {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .chrome-star-icon {
      width: 14px;
      height: 13px;
    }

    .chrome-share-icon {
      width: 12px;
      height: 15px;
    }

    .chrome-extension-icon,
    .chrome-profile-icon,
    .chrome-menu-icon {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .chrome-extension-icon {
      width: 15px;
      height: 15px;
      cursor: pointer;
    }

    .chrome-profile-icon {
      width: 18px;
      height: 18px;
    }

    .chrome-menu-icon {
      width: 3px;
      height: 14px;
    }

    .chrome-divider {
      height: 1px;
      background: #DBDCDD;
    }

    /* Chrome Content Area */
    .chrome-content {
      flex: 1;
      background: #f0ebf8;
      overflow: auto;
      position: relative;
    }

    /* Simulated Webpage */
    .webpage-content {
      padding: 40px;
      max-width: 800px;
      margin: 0 auto;
    }

    .webpage-header {
      background: white;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 12px;
      border-top: 10px solid #673ab7;
    }

    .form-title {
      font-size: 32px;
      font-weight: 400;
      color: #202124;
      margin-bottom: 8px;
    }

    .form-description {
      font-size: 14px;
      color: #5f6368;
      line-height: 1.6;
    }

    .webpage-form {
      background: white;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 12px;
    }

    .form-field {
      margin-bottom: 24px;
    }

    .form-field:last-child {
      margin-bottom: 0;
    }

    .form-field label {
      display: block;
      font-size: 14px;
      font-weight: 400;
      color: #202124;
      margin-bottom: 8px;
    }

    .form-field label .required {
      color: #d93025;
      margin-left: 4px;
    }

    .form-field input,
    .form-field textarea,
    .form-field select {
      width: 100%;
      padding: 12px 16px;
      border: none;
      border-bottom: 1px solid #dadce0;
      font-size: 14px;
      font-family: inherit;
      background: transparent;
      transition: border-color 0.2s;
    }

    .form-field input:focus,
    .form-field textarea:focus,
    .form-field select:focus {
      outline: none;
      border-bottom: 2px solid #673ab7;
    }

    .form-field textarea {
      resize: vertical;
      min-height: 80px;
      border: 1px solid #dadce0;
      border-radius: 4px;
      padding: 12px 16px;
    }

    .form-field textarea:focus {
      border: 2px solid #673ab7;
    }

    .form-field select {
      cursor: pointer;
      background: white;
      border: 1px solid #dadce0;
      border-radius: 4px;
    }

    .submit-button {
      background: #673ab7;
      color: white;
      border: none;
      padding: 10px 24px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background 0.2s;
    }

    .submit-button:hover {
      background: #5e35b1;
    }

    /* Demo Controls */
    .demo-controls {
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(255, 255, 255, 0.95);
      padding: 16px 24px;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
      justify-content: center;
      z-index: 10000;
      backdrop-filter: blur(10px);
    }

    .demo-controls button {
      padding: 10px 20px;
      border: none;
      border-radius: 8px;
      background: #2962FF;
      color: white;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
    }

    .demo-controls button:hover {
      background: #1E4EE3;
      transform: translateY(-1px);
    }

    .demo-controls button.close-popup {
      background: #dc2626;
    }

    .demo-controls button.close-popup:hover {
      background: #b91c1c;
    }

    /* Popup Container - absolute定位相对于chrome-browser，往左移 */
    .fillify-popup {
      position: absolute;
      bottom: -400px;
      right: 40px;
      width: 380px;
      min-height: 370px;
      background: rgba(245, 245, 245, 0.92);
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      border-radius: 16px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      overflow: hidden;
      z-index: 100;
      opacity: 0;
      transition: bottom 0.4s cubic-bezier(0.4, 0, 0.2, 1),
                  opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .fillify-popup.show {
      bottom: 20px;
      opacity: 1;
    }

    /* Header */
    .fillify-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 20px;
    }

    .fillify-header h1 {
      margin: 0;
      color: #2962FF;
      font-size: 22px;
    }

    .fillify-header-right {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .fillify-settings-button {
      padding: 8px;
      border-radius: 4px;
      background: none;
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      transition: background 0.2s;
    }

    .fillify-settings-button:hover {
      background: rgba(0, 0, 0, 0.05);
    }

    #fillify-close-btn {
      padding: 4px;
      border-radius: 4px;
      background: none;
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      transition: background 0.2s;
    }

    #fillify-close-btn:hover {
      background: rgba(0, 0, 0, 0.1);
    }

    /* Form Container */
    .fillify-form-container {
      background: white;
      border-radius: 16px;
      margin: 0 12px 12px 12px;
      padding: 20px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    /* Mode Buttons */
    .fillify-mode-buttons {
      position: relative;
      display: flex;
      gap: 4px;
      padding: 4px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .fillify-mode-buttons::after {
      content: '';
      position: absolute;
      top: 4px;
      left: 4px;
      width: calc(50% - 6px);
      height: calc(100% - 8px);
      background: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 0;
    }

    .fillify-mode-btn {
      flex: 1;
      padding: 8px 12px;
      border: none;
      background: transparent;
      color: #666;
      font-size: 14px;
      cursor: pointer;
      border-radius: 6px;
      position: relative;
      z-index: 1;
      font-weight: 400;
    }

    .fillify-mode-btn.active {
      color: #2962FF;
      font-weight: 500;
    }

    /* Textarea */
    .fillify-textarea {
      width: 100%;
      padding: 14px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      font-size: 14px;
      line-height: 1.5;
      resize: none;
      font-family: inherit;
      height: 220px;
    }

    .fillify-textarea:focus {
      outline: none;
      border-color: #2962FF;
      box-shadow: 0 0 0 3px rgba(41, 98, 255, 0.1);
    }

    /* Primary Button */
    .fillify-primary-btn {
      width: 100%;
      padding: 8px;
      border: none;
      border-radius: 8px;
      background: #2962FF;
      color: white;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      min-height: 40px;
    }

    .fillify-primary-btn:hover:not(.success):not(.loading):not(.thinking):not(.stopping):not(:disabled) {
      background: #1E4EE3;
    }

    /* Button States */
    .fillify-primary-btn.loading {
      background: #1E4EE3;
      cursor: not-allowed;
    }

    .fillify-primary-btn.thinking {
      background: #1E4EE3;
      cursor: not-allowed;
    }

    .fillify-primary-btn.stopping {
      background: #dc2626;
      cursor: not-allowed;
    }

    .fillify-primary-btn.success {
      background: #2962FF;
      cursor: default;
    }

    /* Sparkle Icon */
    .fillify-sparkle-icon {
      width: 16px;
      height: 16px;
      color: inherit;
      opacity: 0.9;
      animation: sparkle 2s ease-in-out infinite;
      margin-left: 4px;
      transition: opacity 0.3s ease;
    }

    .fillify-sparkle-icon.hidden {
      opacity: 0;
      pointer-events: none;
    }

    @keyframes sparkle {
      0%, 100% {
        transform: scale(1);
        opacity: 0.9;
      }
      50% {
        transform: scale(1.1);
        opacity: 1;
      }
    }

    /* Loading Animation */
    .fillify-animation {
      display: none;
      position: absolute;
      border-radius: 100%;
      animation: ripple 0.6s linear infinite;
    }

    .fillify-primary-btn.loading .fillify-animation,
    .fillify-primary-btn.thinking .fillify-animation {
      display: block;
    }

    @keyframes ripple {
      0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.1),
                   0 0 0 40px rgba(255, 255, 255, 0.1),
                   0 0 0 80px rgba(255, 255, 255, 0.1),
                   0 0 0 120px rgba(255, 255, 255, 0.1);
      }
      100% {
        box-shadow: 0 0 0 40px rgba(255, 255, 255, 0.1),
                   0 0 0 80px rgba(255, 255, 255, 0.1),
                   0 0 0 120px rgba(255, 255, 255, 0.1),
                   0 0 0 160px rgba(255, 255, 255, 0);
      }
    }

    /* Stopping Animation (Red Ripple) */
    .fillify-primary-btn.stopping .fillify-animation {
      animation: ripple-stop 0.6s linear infinite;
    }

    @keyframes ripple-stop {
      0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.2),
                   0 0 0 40px rgba(255, 255, 255, 0.2),
                   0 0 0 80px rgba(255, 255, 255, 0.2),
                   0 0 0 120px rgba(255, 255, 255, 0.2);
      }
      100% {
        box-shadow: 0 0 0 40px rgba(255, 255, 255, 0.2),
                   0 0 0 80px rgba(255, 255, 255, 0.2),
                   0 0 0 120px rgba(255, 255, 255, 0.2),
                   0 0 0 160px rgba(255, 255, 255, 0);
      }
    }

    /* Reasoning Bubble - absolute定位相对于chrome-browser，位置在popup上方，往左移 */
    .fillify-reasoning-card {
      position: absolute;
      right: 40px;
      bottom: 440px;
      width: 380px;
      height: 150px;
      border-radius: 14px;
      overflow: hidden;
      opacity: 0;
      transform: scale(0.8) translateY(10px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      pointer-events: auto;
      z-index: 101;
    }

    .fillify-reasoning-card.show {
      opacity: 1;
      transform: scale(1) translateY(0);
    }

    .fillify-reasoning-blob {
      position: absolute;
      z-index: 1;
      width: 150px;
      height: 150px;
      border-radius: 50%;
      background: #2962FF;
      opacity: 1;
      filter: blur(12px);
      animation: fillify-blob-bounce 3s linear infinite;
      will-change: transform;
    }

    @keyframes fillify-blob-bounce {
      0%, 100% { transform: translate(-50%, -50%); top: 0%; left: 0%; }
      25%      { transform: translate(-50%, -50%); top: 0%; left: 100%; }
      50%      { transform: translate(-50%, -50%); top: 100%; left: 100%; }
      75%      { transform: translate(-50%, -50%); top: 100%; left: 0%; }
    }

    .fillify-reasoning-bg {
      position: absolute;
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      z-index: 2;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(24px);
      border-radius: 10px;
      outline: 2px solid white;
      padding: 16px 20px;
    }

    .fillify-reasoning-close {
      position: absolute;
      top: 8px;
      right: 8px;
      width: 24px;
      height: 24px;
      border: none;
      background: transparent;
      color: #999;
      font-size: 20px;
      cursor: pointer;
      z-index: 3;
    }

    .fillify-reasoning-close:hover {
      color: #333;
    }

    .fillify-reasoning-content {
      font-size: 13px;
      line-height: 1.5;
      color: #666;
      height: 100px;
      overflow-y: auto;
      margin-top: 8px;
      padding-right: 8px;
    }

    /* Field Generating Effect - 移除表单字段的呼吸动画 */
    .form-field input.generating,
    .form-field textarea.generating {
      border-color: #1a73e8;
    }

    /* Confetti Canvas */
    .fillify-confetti-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1000;
    }
  </style>
</head>
<body>
<!-- Chrome Browser Frame -->
  <div class="chrome-browser">
    <!-- Top Bar (macOS style) -->
    <div class="chrome-topbar">
      <div class="chrome-dots">
        <div class="chrome-dot red"></div>
        <div class="chrome-dot yellow"></div>
        <div class="chrome-dot green"></div>
      </div>

      <!-- Tab Bar (Inside topbar) -->
      <div class="chrome-tabbar">
        <div class="chrome-tab">
          <div class="chrome-tab-fade"></div>
          <div class="chrome-tab-close">
            <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M0.75 0.75L3.75 3.75M3.75 3.75L6.75 0.75M3.75 3.75L0.75 6.75M3.75 3.75L6.75 6.75" stroke="#3D4043" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="chrome-tab-title-wrapper">
            <div class="chrome-tab-title">Sell or Rent Your Items - Marketplace</div>
          </div>
        </div>
      </div>

      <!-- New Tab Button -->
      <div class="chrome-new-tab-btn">
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M5.75 0.75V5.75M5.75 10.75V5.75M5.75 5.75H0.75M5.75 5.75H10.75" stroke="#3D4043" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>

      <!-- Window Controls -->
      <div class="chrome-window-controls">
        <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M0.75 0.75L4.75 4.75L8.75 0.75" stroke="#3D4043" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>

    <!-- Address Bar -->
    <div class="chrome-addressbar">
      <!-- Back Button -->
      <div class="chrome-back-btn">
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12.75 6.75H0.75M0.75 6.75L6.75 0.75M0.75 6.75L6.75 12.75" stroke="#606367" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>

      <!-- Forward Button -->
      <div class="chrome-forward-btn">
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M0.75 6.75H12.75M12.75 6.75L6.75 0.75M12.75 6.75L6.75 12.75" stroke="#606367" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>

      <!-- Refresh Button -->
      <div class="chrome-refresh-btn">
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M13 7C13 10.3137 10.3137 13 7 13C3.68629 13 1 10.3137 1 7C1 3.68629 3.68629 1 7 1C9.09894 1 10.9543 2.08885 12 3.75M12 1V3.75M12 3.75H9.25" stroke="#606367" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>

      <!-- URL Bar -->
      <div class="chrome-url-bar">
        <div class="chrome-lock-icon">
          <svg width="8" height="11" viewBox="0 0 8 11" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M4 0C2.61929 0 1.5 1.11929 1.5 2.5V3.5H1C0.447715 3.5 0 3.94772 0 4.5V9.5C0 10.0523 0.447715 10.5 1 10.5H7C7.55228 10.5 8 10.0523 8 9.5V4.5C8 3.94772 7.55228 3.5 7 3.5H6.5V2.5C6.5 1.11929 5.38071 0 4 0ZM5.5 3.5V2.5C5.5 1.67157 4.82843 1 4 1C3.17157 1 2.5 1.67157 2.5 2.5V3.5H5.5Z" fill="#606367"/>
          </svg>
        </div>
        <div class="chrome-url-text-wrapper">
          <div class="chrome-url">marketplace.example.com/post-listing</div>
        </div>
        <div class="chrome-star-icon">
          <svg width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M6.13798 0.449139C6.33256 -0.149712 7.17978 -0.149714 7.37436 0.449139L8.6696 4.43549H12.8611C13.4908 4.43549 13.7526 5.24125 13.2432 5.61135L9.85217 8.07505L11.1474 12.0614C11.342 12.6602 10.6566 13.1582 10.1472 12.7881L6.75617 10.3244L3.36518 12.7881C2.85577 13.1582 2.17035 12.6602 2.36493 12.0614L3.66018 8.07505L0.269185 5.61135C-0.240227 5.24124 0.0215741 4.43549 0.651246 4.43549H4.84274L6.13798 0.449139ZM6.75617 2.59164L5.89685 5.23635C5.80983 5.50417 5.56026 5.68549 5.27867 5.68549H2.49786L4.74758 7.32001C4.9754 7.48553 5.07072 7.77892 4.98371 8.04673L4.12439 10.6914L6.37411 9.05692C6.60193 8.8914 6.91042 8.8914 7.13823 9.05692L9.38795 10.6914L8.52864 8.04673C8.44162 7.77891 8.53695 7.48553 8.76476 7.32001L11.0145 5.68549H8.23368C7.95208 5.68549 7.70251 5.50416 7.61549 5.23635L6.75617 2.59164Z" fill="#626365"/>
          </svg>
        </div>
        <div class="chrome-share-icon">
          <svg width="12" height="15" viewBox="0 0 12 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M6.06694 0.183058C5.82286 -0.0610194 5.42714 -0.0610194 5.18306 0.183058L3.18306 2.18306C2.93898 2.42714 2.93898 2.82286 3.18306 3.06694C3.42714 3.31102 3.82286 3.31102 4.06694 3.06694L5 2.13388V8.625C5 8.97018 5.27982 9.25 5.625 9.25C5.97018 9.25 6.25 8.97018 6.25 8.625V2.13388L7.18306 3.06694C7.42714 3.31102 7.82286 3.31102 8.06694 3.06694C8.31102 2.82286 8.31102 2.42714 8.06694 2.18306L6.06694 0.183058ZM1.25 6.125C1.25 5.64175 1.64175 5.25 2.125 5.25H3.125C3.47018 5.25 3.75 4.97018 3.75 4.625C3.75 4.27982 3.47018 4 3.125 4H2.125C0.951395 4 0 4.9514 0 6.125V12.125C0 13.2986 0.951395 14.25 2.125 14.25H9.125C10.2986 14.25 11.25 13.2986 11.25 12.125V6.125C11.25 4.9514 10.2986 4 9.125 4H8.125C7.77982 4 7.5 4.27982 7.5 4.625C7.5 4.97018 7.77982 5.25 8.125 5.25H9.125C9.60825 5.25 10 5.64175 10 6.125V12.125C10 12.6082 9.60825 13 9.125 13H2.125C1.64175 13 1.25 12.6082 1.25 12.125V6.125Z" fill="#626365"/>
          </svg>
        </div>
      </div>

      <!-- Extension Icon -->
      <div class="chrome-extension-icon" onclick="togglePopup()" title="Fillify">
        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 2.5H10C11.1046 2.5 12 3.39543 12 4.5V7H13.5C14.3284 7 15 7.67157 15 8.5C15 9.32843 14.3284 10 13.5 10H12V12.5C12 13.6046 11.1046 14.5 10 14.5H7.5V13C7.5 12.1716 6.82843 11.5 6 11.5C5.17157 11.5 4.5 12.1716 4.5 13V14.5H2C0.895431 14.5 0 13.6046 0 12.5V10H1.5C2.32843 10 3 9.32843 3 8.5C3 7.67157 2.32843 7 1.5 7H0V4.5C0 3.39543 0.895431 2.5 2 2.5H4.5V1.5C4.5 0.671573 5.17157 0 6 0C6.82843 0 7.5 0.671573 7.5 1.5V2.5Z" fill="#606367"/>
        </svg>
      </div>

      <!-- Profile Icon -->
      <div class="chrome-profile-icon">
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="9" cy="9" r="9" fill="#D9D9D9"/>
          <circle cx="3.41947" cy="3.41947" r="3.41947" transform="matrix(1 0 0 -1 5.58057 9.72986)" fill="#757575"/>
          <path d="M2.55347 14.3225C2.55347 12.1136 4.34415 10.3229 6.55306 10.3229H11.4468C13.6557 10.3229 15.4464 12.1136 15.4464 14.3225H2.55347Z" fill="#757575"/>
        </svg>
      </div>

      <!-- Menu Icon -->
      <div class="chrome-menu-icon">
        <svg width="3" height="14" viewBox="0 0 3 14" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M3 1.5C3 2.32843 2.32843 3 1.5 3C0.671573 3 0 2.32843 0 1.5C0 0.671573 0.671573 0 1.5 0C2.32843 0 3 0.671573 3 1.5ZM3 6.75C3 7.57843 2.32843 8.25 1.5 8.25C0.671573 8.25 0 7.57843 0 6.75C0 5.92157 0.671573 5.25 1.5 5.25C2.32843 5.25 3 5.92157 3 6.75ZM1.5 13.5C2.32843 13.5 3 12.8284 3 12C3 11.1716 2.32843 10.5 1.5 10.5C0.671573 10.5 0 11.1716 0 12C0 12.8284 0.671573 13.5 1.5 13.5Z" fill="#606367"/>
        </svg>
      </div>
    </div>

    <!-- Divider -->
    <div class="chrome-divider"></div>

    <!-- Content Area -->
    <div class="chrome-content">
      <div class="webpage-content">
        <div class="webpage-header">
          <h1 class="form-title">Sell or Rent Your Item</h1>
          <p class="form-description">Fill out this form to list your item for sale or rent. Please provide accurate information to help buyers/renters.</p>
        </div>

        <div class="webpage-form">
          <div class="form-field">
            <label for="item-title">Item Title <span class="required">*</span></label>
            <input type="text" id="item-title" placeholder="e.g., iPhone 13 Pro Max - 256GB">
          </div>

          <div class="form-field">
            <label for="item-category">Category <span class="required">*</span></label>
            <input type="text" id="item-category" placeholder="e.g., Electronics, Furniture, Clothing">
          </div>

          <div class="form-field">
            <label for="item-description">Description <span class="required">*</span></label>
            <textarea id="item-description" placeholder="Describe your item's condition, features, and any other relevant details..."></textarea>
          </div>

          <div class="form-field">
            <label for="item-condition">Condition <span class="required">*</span></label>
            <input type="text" id="item-condition" placeholder="e.g., Like New, Good, Fair">
          </div>

          <div class="form-field">
            <label for="item-price">Price <span class="required">*</span></label>
            <input type="text" id="item-price" placeholder="e.g., $500 or $50/month">
          </div>

          <div class="form-field">
            <label for="listing-type">Listing Type <span class="required">*</span></label>
            <input type="text" id="listing-type" placeholder="e.g., For Sale, For Rent">
          </div>

          <div class="form-field">
            <label for="contact-email">Contact Email <span class="required">*</span></label>
            <input type="email" id="contact-email" placeholder="<EMAIL>">
          </div>

          <div class="form-field">
            <label for="contact-phone">Contact Phone</label>
            <input type="tel" id="contact-phone" placeholder="(*************">
          </div>

          <button type="submit" class="submit-button">Submit Listing</button>
        </div>
      </div>
    </div>

    <!-- Fillify Popup (absolute positioning inside chrome browser) -->
    <div id="fillify-popup" class="fillify-popup">
        <!-- Header -->
        <div class="fillify-header">
          <h1>Fillify</h1>
          <div class="fillify-header-right">
            <button class="fillify-settings-button">
              <div style="width: 18px; height: 14px; position: relative; display: flex; flex-direction: column; justify-content: space-between;">
                <span style="display: block; width: 100%; height: 2px; background-color: #666; border-radius: 2px;"></span>
                <span style="display: block; width: 100%; height: 2px; background-color: #666; border-radius: 2px;"></span>
                <span style="display: block; width: 100%; height: 2px; background-color: #666; border-radius: 2px;"></span>
              </div>
            </button>
            <button id="fillify-close-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="#666">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- Form Container -->
        <form class="fillify-form-container">
          <!-- Mode Buttons -->
          <div class="fillify-mode-buttons">
            <button type="button" class="fillify-mode-btn active">Smart</button>
            <button type="button" class="fillify-mode-btn">
              Custom
              <svg class="fillify-custom-dropdown-icon" viewBox="0 0 12 8" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="width: 8px; height: 6px; margin-left: 4px;">
                <polyline points="1,1 6,6 11,1"/>
              </svg>
            </button>
          </div>

          <!-- Description Input Container with Bottom Toolbar -->
          <div style="position: relative;">
            <textarea id="fillify-description" class="fillify-textarea" placeholder="Describe what you want to fill in..."></textarea>

            <!-- Bottom toolbar: Language & Reference selectors -->
            <div class="fillify-bottom-toolbar" style="position: absolute; bottom: 8px; left: 14px; right: 14px; display: flex; justify-content: flex-end; align-items: center; height: 26px; pointer-events: none;">
              <!-- Right: Language and Reference icons -->
              <div style="display: flex; gap: 12px; align-items: center; pointer-events: auto;">
                <!-- Reference selector -->
                <div class="fillify-reference-selector">
                  <div class="fillify-reference-flex" style="position: relative; display: flex; align-items: center; gap: 4px; padding: 0 4px; height: 26px; border-radius: 4px; cursor: pointer;">
                    <svg class="fillify-reference-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 16px; height: 16px; flex-shrink: 0; stroke: #4A5056;">
                      <path d="M5 4C5 3.44772 5.44772 3 6 3H18C18.5523 3 19 3.44772 19 4V21L12 17.5L5 21V4Z" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                </div>

                <!-- Language selector -->
                <div class="fillify-language-selector">
                  <div class="fillify-language-flex" style="position: relative; display: flex; align-items: center; gap: 2px; padding: 0 4px; height: 26px; border-radius: 4px; cursor: pointer;">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 16px; height: 16px;">
                      <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M3.6001 9H20.4001" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M3.6001 15H20.4001" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M12 21C13.6569 21 15 16.9706 15 12C15 7.02944 13.6569 3 12 3C10.3432 3 9 7.02944 9 12C9 16.9706 10.3432 21 12 21Z" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <div class="fillify-selected-language" style="color: #666; font-size: 12px; margin-right: 2px;">Auto</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Generate Button -->
          <button id="fillify-fill-button" type="button" class="fillify-primary-btn">
            <div class="fillify-button-content" style="display: flex; align-items: center; gap: 6px; position: relative; z-index: 1;">
              <span class="fillify-button-text">Generate</span>
              <svg class="fillify-sparkle-icon" viewBox="0 0 24 24" fill="currentColor">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4.5a.75.75 0 01.721.544l.813 2.846a3.75 3.75 0 002.576 2.576l2.846.813a.75.75 0 010 1.442l-2.846.813a3.75 3.75 0 00-2.576 2.576l-.813 2.846a.75.75 0 01-1.442 0l-.813-2.846a3.75 3.75 0 00-2.576-2.576l-2.846-.813a.75.75 0 010-1.442l2.846-.813a3.75 3.75 0 002.576-2.576l.813-2.846A.75.75 0 019 4.5zM18 1.5a.75.75 0 01.728.568l.258 1.036c.236.94.97 1.674 1.91 1.91l1.036.258a.75.75 0 010 1.456l-1.036.258c-.94.236-1.674.97-1.91 1.91l-.258 1.036a.75.75 0 01-1.456 0l-.258-1.036a2.625 2.625 0 00-1.91-1.91l-1.036-.258a.75.75 0 010-1.456l1.036-.258a2.625 2.625 0 001.91-1.91l.258-1.036A.75.75 0 0118 1.5zM16.5 15a.75.75 0 01.712.513l.394 1.183c.15.447.5.799.948.948l1.183.395a.75.75 0 010 1.422l-1.183.395c-.447.15-.799.5-.948.948l-.395 1.183a.75.75 0 01-1.422 0l-.395-1.183a1.5 1.5 0 00-.948-.948l-1.183-.395a.75.75 0 010-1.422l1.183-.395c.447-.15.799-.5.948-.948l.395-1.183A.75.75 0 0116.5 15z"></path>
              </svg>
            </div>
            <div class="fillify-animation"></div>
          </button>

          <!-- Confetti Canvas -->
          <canvas class="fillify-confetti-canvas"></canvas>
    </form>
  </div>

  <!-- Reasoning Bubble (absolute positioning inside chrome browser) -->
  <div id="reasoning-bubble" class="fillify-reasoning-card">
    <div class="fillify-reasoning-blob"></div>
    <div class="fillify-reasoning-bg">
      <button class="fillify-reasoning-close">×</button>
      <div class="fillify-reasoning-content">
        Analyzing the form structure and context... Understanding the field relationships... Generating appropriate content based on your description...
      </div>
    </div>
  </div>
  </div>

  <script>
    const popup = document.getElementById('fillify-popup');
    const button = document.getElementById('fillify-fill-button');
    const buttonText = button.querySelector('.fillify-button-text');
    const sparkleIcon = button.querySelector('.fillify-sparkle-icon');
    const textarea = document.getElementById('fillify-description');
    const reasoningBubble = document.getElementById('reasoning-bubble');

    // Auto-loop animation configuration
    let animationTimeout = null;
    const promptText = "Help me list my MacBook Pro 2021 (M1, 16GB RAM, 512GB SSD) for sale - excellent condition, barely used, with original box and charger. Looking for around $1200.";

    const formData = {
      'item-title': 'MacBook Pro 2021 - M1 Chip, 16GB RAM, 512GB SSD',
      'item-category': 'Electronics',
      'item-description': 'Excellent condition MacBook Pro purchased in 2021. Barely used, always kept in protective case. Includes original box, charger, and all accessories. Perfect for students or professionals. No scratches or dents.',
      'item-condition': 'Like New',
      'item-price': '$1,200',
      'listing-type': 'For Sale',
      'contact-email': '<EMAIL>',
      'contact-phone': '(*************'
    };

    function setState(state) {
      // Reset all states
      button.classList.remove('loading', 'thinking', 'stopping', 'success');
      button.disabled = false;
      sparkleIcon.classList.remove('hidden');
      textarea.classList.remove('generating');

      switch(state) {
        case 'generate':
          buttonText.textContent = 'Generate';
          break;

        case 'generating':
          button.classList.add('loading');
          button.disabled = true;
          buttonText.textContent = 'Generating...';
          sparkleIcon.classList.add('hidden');
          textarea.classList.add('generating');
          break;

        case 'thinking':
          button.classList.add('thinking');
          button.disabled = true;
          buttonText.textContent = 'Thinking...';
          sparkleIcon.classList.add('hidden');
          textarea.classList.add('generating');
          break;

        case 'stopping':
          button.classList.add('stopping');
          button.disabled = true;
          buttonText.textContent = 'Stopping...';
          sparkleIcon.classList.add('hidden');
          break;

        case 'finish':
          button.classList.add('success');
          buttonText.textContent = 'Finish';
          sparkleIcon.classList.add('hidden');
          // Show confetti animation
          createConfetti();
          break;
      }
    }

    function showReasoningBubble() {
      reasoningBubble.classList.add('show');
    }

    function hideReasoningBubble() {
      reasoningBubble.classList.remove('show');
    }

    function typeText(element, text, speed = 30) {
      return new Promise(resolve => {
        let index = 0;
        element.value = '';

        const interval = setInterval(() => {
          if (index < text.length) {
            element.value += text[index];
            // Auto scroll textarea to bottom
            element.scrollTop = element.scrollHeight;
            index++;
          } else {
            clearInterval(interval);
            resolve();
          }
        }, speed);
      });
    }

    function typeIntoField(fieldId, text, speed = 20) {
      return new Promise(resolve => {
        const field = document.getElementById(fieldId);
        if (!field) {
          resolve();
          return;
        }

        // Scroll the form to make the field visible
        field.scrollIntoView({ behavior: 'smooth', block: 'center' });

        field.classList.add('generating');
        let index = 0;
        field.value = '';

        const interval = setInterval(() => {
          if (index < text.length) {
            field.value += text[index];
            index++;
          } else {
            clearInterval(interval);
            field.classList.remove('generating');
            resolve();
          }
        }, speed);
      });
    }

    function updateReasoningText(text) {
      const content = reasoningBubble.querySelector('.fillify-reasoning-content');
      if (content) {
        content.textContent = text;
        // Auto scroll to bottom
        content.scrollTop = content.scrollHeight;
      }
    }

    // 流式输出推理内容
    function streamReasoningText(text, speed = 30) {
      return new Promise(resolve => {
        const content = reasoningBubble.querySelector('.fillify-reasoning-content');
        if (!content) {
          resolve();
          return;
        }

        let index = 0;
        content.textContent = '';

        const interval = setInterval(() => {
          if (index < text.length) {
            content.textContent += text[index];
            // Auto scroll to bottom
            content.scrollTop = content.scrollHeight;
            index++;
          } else {
            clearInterval(interval);
            resolve();
          }
        }, speed);
      });
    }

    async function runAutoLoop() {
      // Scroll page back to top when starting new loop
      const chromeContent = document.querySelector('.chrome-content');
      if (chromeContent) {
        chromeContent.scrollTo({ top: 0, behavior: 'smooth' });
      }

      // Step 1: Show popup (slide up)
      popup.classList.add('show');
      await new Promise(resolve => setTimeout(resolve, 800));

      // Step 2: Type prompt in textarea
      await typeText(textarea, promptText, 30);
      await new Promise(resolve => setTimeout(resolve, 500));

      // Step 3: Click generate button -> generating state
      setState('generating');
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Step 4: Transition to thinking state with streaming reasoning
      setState('thinking');
      showReasoningBubble();

      // Stream reasoning text in first person (English)
      const reasoningText = "Alright, let me analyze this listing form... I need to fill out information for a MacBook Pro sale. First, let me understand the details: 2021 model, M1 chip, 16GB RAM, 512GB SSD... These are all important specs. The user mentioned excellent condition, barely used, includes original box and charger, asking around $1200... I should create an attractive title and compelling description. Let me think about the best way to present this information to potential buyers...";
      await streamReasoningText(reasoningText, 35);
      await new Promise(resolve => setTimeout(resolve, 800));

      // Step 5: Thinking complete, hide bubble
      hideReasoningBubble();
      await new Promise(resolve => setTimeout(resolve, 300));

      // Step 6: Change to finish state and show confetti before filling form
      setState('finish');

      // Step 7: Fill form fields one by one
      await typeIntoField('item-title', formData['item-title'], 15);
      await new Promise(resolve => setTimeout(resolve, 300));

      // Category (now text field with typing animation)
      await typeIntoField('item-category', formData['item-category'], 20);
      await new Promise(resolve => setTimeout(resolve, 300));

      await typeIntoField('item-description', formData['item-description'], 12);
      await new Promise(resolve => setTimeout(resolve, 300));

      // Condition (now text field with typing animation)
      await typeIntoField('item-condition', formData['item-condition'], 25);
      await new Promise(resolve => setTimeout(resolve, 300));

      await typeIntoField('item-price', formData['item-price'], 20);
      await new Promise(resolve => setTimeout(resolve, 300));

      // Listing type (now text field with typing animation)
      await typeIntoField('listing-type', formData['listing-type'], 25);
      await new Promise(resolve => setTimeout(resolve, 300));

      await typeIntoField('contact-email', formData['contact-email'], 20);
      await new Promise(resolve => setTimeout(resolve, 300));

      await typeIntoField('contact-phone', formData['contact-phone'], 25);
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Step 8: Reset and hide popup
      setState('generate');
      textarea.value = '';

      // Clear all form fields
      Object.keys(formData).forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
          field.value = '';
        }
      });

      popup.classList.remove('show');
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Loop again
      runAutoLoop();
    }

    // Confetti animation function
    function createConfetti() {
      const canvas = document.querySelector('.fillify-confetti-canvas');
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      const popupEl = document.getElementById('fillify-popup');
      if (!popupEl) return;

      const rect = popupEl.getBoundingClientRect();
      canvas.width = rect.width;
      canvas.height = rect.height;

      const confettiConfig = {
        confettiCount: 20,
        colors: [
          { front: '#7b5cff', back: '#6245e0' },
          { front: '#b3c7ff', back: '#8fa5e5' },
          { front: '#5c86ff', back: '#345dd1' }
        ]
      };

      let confetti = [];

      // Create confetti particles
      for (let i = 0; i < confettiConfig.confettiCount; i++) {
        confetti.push({
          x: canvas.width / 2,
          y: canvas.height / 2,
          vx: (Math.random() - 0.5) * 10,
          vy: Math.random() * -10 - 5,
          color: confettiConfig.colors[Math.floor(Math.random() * confettiConfig.colors.length)].front,
          size: Math.random() * 6 + 4
        });
      }

      const animate = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        confetti.forEach((particle, index) => {
          particle.x += particle.vx;
          particle.y += particle.vy;
          particle.vy += 0.3; // gravity

          ctx.fillStyle = particle.color;
          ctx.fillRect(particle.x, particle.y, particle.size, particle.size);

          if (particle.y > canvas.height + 100) {
            confetti.splice(index, 1);
          }
        });

        if (confetti.length > 0) {
          requestAnimationFrame(animate);
        }
      };

      animate();
    }

    // Start the auto-loop animation
    setTimeout(() => {
      runAutoLoop();
    }, 1000);
  </script>
</body>
</html>