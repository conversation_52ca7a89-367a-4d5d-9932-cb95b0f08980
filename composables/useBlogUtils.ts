/**
 * Blog utility composable
 * Provides reusable functions for blog posts
 */

export const useBlogUtils = () => {
  /**
   * Format date to human-readable string
   */
  const formatDate = (value?: string): string => {
    if (!value) return ''
    const d = new Date(value)
    if (Number.isNaN(d.getTime())) return ''
    return d.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  /**
   * Extract text content from content node recursively
   */
  const getTextContent = (node: any): string => {
    if (node.type === 'text') {
      return node.value || ''
    }
    if (node.children && Array.isArray(node.children)) {
      return node.children.map(getTextContent).join('')
    }
    return ''
  }

  /**
   * Calculate word count for article
   * Handles both English words and Chinese characters
   */
  const getWordCount = (body: any): number => {
    if (!body || !body.children) return 0

    const totalText = getTextContent(body)
    // Count English words
    const englishWords = totalText.match(/\b\w+\b/g)?.length || 0
    // Count Chinese characters
    const chineseChars = totalText.replace(/[^\u4e00-\u9fff]/g, '').length

    return englishWords + chineseChars
  }

  /**
   * Estimate reading time (based on ~400 characters per minute)
   */
  const getReadTime = (body: any): number => {
    if (!body || !body.children) return 1

    const totalText = getTextContent(body)
    const wordCount = totalText.length
    const readTime = Math.max(1, Math.ceil(wordCount / 400))

    return readTime
  }

  return {
    formatDate,
    getTextContent,
    getWordCount,
    getReadTime
  }
}
