import { useState } from 'nuxt/app'
import { computed, watch } from 'vue'
import { cookieUtil } from '~/utils/cookie'
import { useSupabase } from '~/composables/useSupabase'

// 定义用户类型
type User = {
  id: string
  email: string
  full_name: string
  picture_url?: string
  credits?: number
}

// 获取用户显示名称的工具函数
const getDisplayName = (user: User): string => {
  if (user.full_name && user.full_name.trim() && user.full_name !== 'User') {
    return user.full_name
  }
  if (user.email) {
    return user.email.split('@')[0]
  }
  return 'User'
}

// 定义可能的域名配置
const DOMAINS: (string | undefined)[] = [
  undefined, // 默认域名
  'localhost',
  'fillify.tech',
  '.fillify.tech'
]

export const useAuth = () => {
  const config = useRuntimeConfig()
  const apiBase = config.public.apiBase
  const user = useState<User | null>('user', () => null)
  const isLoggedIn = computed(() => !!user.value)
  const supabase = useSupabase()

  // 更新全局状态的辅助函数
  const updateGlobalState = (userData: User) => {
    if (process.client) {
      // 更新本地存储
      const userState = {
        isLoggedIn: true,
        credits: userData.credits || 3,
        userId: userData.id,
        email: userData.email,
        fullName: userData.full_name
      }
      localStorage.setItem('userState', JSON.stringify(userState))

      // 触发全局状态更新事件
      window.dispatchEvent(new CustomEvent('userStateUpdate', { detail: userState }))
    }
  }

  // 清除认证状态
  const clearAuthState = () => {
    user.value = null
    if (process.client) {
      // 清除所有可能的域名下的 cookie
      DOMAINS.forEach(domain => {
        cookieUtil.removeCookie('auth-token', { domain })
        cookieUtil.removeCookie('user-data', { domain })
        cookieUtil.removeCookie('xToken', { domain }) // 重要:清除 xToken
      })

      // 清除本地存储
      localStorage.removeItem('userState')

      // 触发全局状态更新事件
      window.dispatchEvent(new CustomEvent('userStateUpdate', {
        detail: { isLoggedIn: false, credits: 0, userId: null, email: null, fullName: null }
      }))
    }
  }

  // 监听 Supabase 认证状态变化
  if (process.client && supabase) {
    // 监听认证状态变化
    supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        // 处理用户登录
        handleUserLogin(session.user)
      } else if (event === 'SIGNED_OUT') {
        // 处理用户登出
        clearAuthState()
      }
    })
  }

  // 处理用户登录（来自 Supabase 或其他来源）
  const handleUserLogin = async (authUser: any) => {
    try {
      if (!supabase) {
        console.error('Supabase client not available')
        return
      }

      // 等待一小段时间让触发器完成用户创建
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 从我们的用户表获取用户数据（应该已经由触发器创建）
      const { data: initialUserData, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .eq('id', authUser.id)
        .single()

      let userData = initialUserData as User | null

      if (fetchError) {
        console.error('Error fetching user data:', fetchError)

        // 如果触发器失败，手动创建用户记录作为后备方案
        if (fetchError.code === 'PGRST116') {
          console.log('User not found, creating manually as fallback...')
          const { data: newUser, error: insertError } = await (supabase
            .from('users')
            .insert({
              id: authUser.id,
              email: authUser.email || `${authUser.id}@temp.supabase.co`,
              full_name: authUser.user_metadata?.full_name || authUser.user_metadata?.name || 'User',
              given_name: authUser.user_metadata?.given_name,
              family_name: authUser.user_metadata?.family_name,
              picture_url: authUser.user_metadata?.avatar_url || authUser.user_metadata?.picture,
              credits: 10.000 // 新用户默认10个积分
            })
            .select()
            .single() as any)

          if (insertError) {
            console.error('Error creating user manually:', insertError)
            throw new Error('Failed to create user account')
          }

          // 手动创建初始积分记录
          try {
            await (supabase
              .from('credit_transactions')
              .insert({
                user_id: authUser.id,
                amount: 10.000,
                type: 'initial',
                description: 'Welcome bonus credits'
              }) as any)
          } catch (creditError) {
            console.error('Error creating initial credits:', creditError)
            // 不阻止用户登录，只记录错误
          }

          userData = newUser as User
        } else {
          throw fetchError
        }
      }

      // 更新用户状态
      if (userData) {
        const typedUserData = userData as User
        user.value = {
          id: typedUserData.id,
          email: typedUserData.email,
          full_name: typedUserData.full_name,
          picture_url: typedUserData.picture_url,
          credits: typedUserData.credits
        }

        // 设置认证 cookie
        cookieUtil.setCookie('xToken', typedUserData.id, {
          expires: 7,
          path: '/'
        })

        updateGlobalState({
          id: typedUserData.id,
          email: typedUserData.email,
          full_name: typedUserData.full_name,
          picture_url: typedUserData.picture_url,
          credits: typedUserData.credits
        })
      }
    } catch (error) {
      console.error('Error handling user login:', error)
      clearAuthState()
    }
  }

  // 发送邮箱验证码
  const sendEmailOTP = async (email: string) => {
    try {
      const response = await $fetch('/api/auth/send-otp', {
        method: 'POST',
        body: { email }
      })
      return response
    } catch (error) {
      console.error('Send OTP error:', error)
      throw error
    }
  }

  // 验证邮箱验证码并登录
  const verifyEmailOTP = async (email: string, token: string) => {
    try {
      const response = await $fetch('/api/auth/verify-otp', {
        method: 'POST',
        body: { email, token }
      }) as any

      if (response.success && response.user) {
        user.value = response.user
        updateGlobalState(response.user)
        return response
      }

      throw new Error('Verification failed')
    } catch (error) {
      console.error('Verify OTP error:', error)
      throw error
    }
  }

  // 使用 Google 登录
  const signInWithGoogle = async () => {
    if (!supabase) {
      throw new Error('Supabase client not initialized')
    }

    // 使用 Supabase Auth 进行 Google 登录
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        }
      }
    })

    return { data, error }
  }



  const signOut = async () => {
    try {
      let accessToken: string | undefined

      // 获取当前的 access token
      if (supabase) {
        const { data: { session } } = await supabase.auth.getSession()
        accessToken = session?.access_token
      }

      // 1. 先调用后端 logout 接口撤销服务端 session
      if (apiBase && accessToken) {
        try {
          await fetch(`${apiBase}/api/auth/logout`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ access_token: accessToken }),
          })
        } catch (err) {
          console.error('Backend logout error:', err)
          // 继续执行客户端退出流程
        }
      }

      // 2. 使用 Supabase 客户端登出
      if (supabase) {
        await supabase.auth.signOut()
      }

      // 3. 清理所有客户端状态
      clearAuthState()

      // 4. 强制刷新页面以确保状态完全清理
      window.location.href = '/'
    } catch (error) {
      console.error('Logout error:', error)
      // 即使出错也清理本地状态
      clearAuthState()
      window.location.href = '/'
    }
  }

  const fetchUserData = async () => {
    const xToken = cookieUtil.getCookie('xToken')
    if (!xToken) {
      clearAuthState()
      return
    }

    try {
      // 优先尝试从外部后端API获取用户数据（保持现有业务逻辑）
      if (apiBase) {
        const res = await fetch(`${apiBase}/api/users/get-user`, {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userId: xToken }),
        })

        if (res.ok) {
          const data = await res.json()
          if (data.success && data.user) {
            user.value = data.user
            updateGlobalState(data.user)
            return
          }
        }
      }

      // 如果外部API不可用或失败，尝试从 Supabase 获取
      if (supabase) {
        const { data: userData, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', xToken)
          .single()

        if (!error && userData) {
          const typedUserData = userData as User
          const userObj = {
            id: typedUserData.id,
            email: typedUserData.email,
            full_name: typedUserData.full_name,
            picture_url: typedUserData.picture_url,
            credits: typedUserData.credits
          }
          user.value = userObj
          updateGlobalState(userObj)
          return
        }
      }

      throw new Error('Failed to fetch user data from all sources')
    } catch (error) {
      console.error('Error fetching user data:', error)

      // 最后的备用方案：使用localStorage
      if (process.client) {
        const userState = localStorage.getItem('userState')
        if (userState) {
          try {
            const parsedState = JSON.parse(userState)
            if (parsedState.isLoggedIn && parsedState.userId) {
              const localUser = {
                id: parsedState.userId,
                email: parsedState.email || '<EMAIL>',
                full_name: parsedState.fullName || (parsedState.email ? parsedState.email.split('@')[0] : 'User'),
                credits: parsedState.credits || 3
              }
              user.value = localUser
              updateGlobalState(localUser)
              return
            }
          } catch (parseError) {
            console.error('Error parsing local user state:', parseError)
          }
        }
      }

      clearAuthState()
    }
  }

  return {
    user,
    isLoggedIn,
    signOut,
    fetchUserData,
    clearAuthState,
    sendEmailOTP,
    verifyEmailOTP,
    signInWithGoogle,
    getDisplayName
  }
} 