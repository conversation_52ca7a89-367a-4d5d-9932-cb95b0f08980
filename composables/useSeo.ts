import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

interface Breadcrumb {
  name: string
  url: string
}

interface SeoInput {
  i18nKey?: string
  canonicalPath?: string
  breadcrumbs?: Breadcrumb[]
  robots?: string
  image?: string
  title?: string
  description?: string
}

interface ResolvedSeoOptions extends SeoInput {
  i18nKey?: string
  canonicalPath: string
  breadcrumbs?: Breadcrumb[]
  robots: string
  image: string
  title?: string
  description?: string
  hasExplicitCanonical: boolean
}

const withLeadingSlash = (value: string) => {
  if (!value) {
    return '/'
  }
  return value.startsWith('/') ? value : `/${value}`
}

const toAbsoluteUrl = (siteUrl: string, path: string) => {
  const normalized = withLeadingSlash(path)
  if (normalized === '/') {
    return `${siteUrl}/`
  }
  return `${siteUrl}${normalized}`
}

const resolveLanguageTag = (localeEntry: any): string => {
  if (!localeEntry) return ''
  return localeEntry.language || localeEntry.iso || localeEntry.code || ''
}

const resolveHrefLang = (localeEntry: any): string => {
  if (!localeEntry) return ''
  return localeEntry.code || localeEntry.language || localeEntry.iso || ''
}

export const useSeo = () => {
  const runtimeConfig = useRuntimeConfig()
  const route = useRoute()
  const switchLocalePath = useSwitchLocalePath()
  const { t, te, locale, locales, defaultLocale } = useI18n()

  const siteUrl = (runtimeConfig.public?.publicUrl || runtimeConfig.public?.siteUrl || 'https://fillify.tech').replace(/\/$/, '')
  const defaultImageWebp = `${siteUrl}/og/og-image.webp`
  const defaultImagePng = `${siteUrl}/og/og-image.png`
  const defaultCoverImage = `${siteUrl}/cover.png`
  const defaultLogo = `${siteUrl}/logo/Fillify-Logo.svg`

  const currentOptions = ref<ResolvedSeoOptions | null>(null)

  const setSeoMeta = (input?: string | SeoInput, legacyBreadcrumbs?: Breadcrumb[]) => {
    let options: SeoInput = {}

    if (typeof input === 'string') {
      const normalizedKey = input.endsWith('.') ? input.slice(0, -1) : input
      options = {
        i18nKey: normalizedKey,
        breadcrumbs: legacyBreadcrumbs
      }
    } else if (input) {
      options = {
        ...input,
        breadcrumbs: input.breadcrumbs ?? legacyBreadcrumbs
      }
    } else if (legacyBreadcrumbs) {
      options = { breadcrumbs: legacyBreadcrumbs }
    }

    const resolved: ResolvedSeoOptions = {
      i18nKey: options.i18nKey,
      canonicalPath: withLeadingSlash(options.canonicalPath ?? route.path ?? '/'),
      breadcrumbs: options.breadcrumbs,
      robots: options.robots ?? 'index,follow',
      image: options.image ?? defaultCoverImage,
      title: options.title,
      description: options.description,
      hasExplicitCanonical: Boolean(options.canonicalPath)
    }

    currentOptions.value = resolved
    updateSeoMeta()
  }

  const updateSeoMeta = () => {
    if (!currentOptions.value) {
      return
    }

    const options = currentOptions.value
    const translationPrefix = options.i18nKey ? `${options.i18nKey}.` : ''

    const translationTitleKey = `${translationPrefix}meta.title`
    const translationDescriptionKey = `${translationPrefix}meta.description`

    const title = options.title ?? (te(translationTitleKey) ? t(translationTitleKey) : t('meta.title'))
    const description = options.description ?? (te(translationDescriptionKey) ? t(translationDescriptionKey) : t('meta.description'))

    const mainKeywords = [
      t('meta.keywords.formFilling'),
      t('meta.keywords.automation'),
      t('meta.keywords.email'),
      t('meta.keywords.bugReport')
    ]

    const hasAdditionalKeywords = te('meta.keywords.additional')
    const additionalKeywords = hasAdditionalKeywords
      ? Array.from({ length: 10 }, (_, i) => t(`meta.keywords.additional.${i}`)).filter(Boolean)
      : []

    const includeAdditionalKeywords = !options.i18nKey
    const keywords = includeAdditionalKeywords ? [...mainKeywords, ...additionalKeywords] : mainKeywords

    const canonicalUrl = toAbsoluteUrl(siteUrl, options.canonicalPath)

    const schemaOrgData = {
      '@context': 'https://schema.org',
      '@type': 'SoftwareApplication',
      name: 'Fillify',
      alternateName: 'Fillify AI Form Filler',
      applicationCategory: 'BrowserExtension',
      applicationSubCategory: 'Productivity',
      operatingSystem: 'Chrome, Edge',
      downloadUrl: 'https://chrome.google.com/webstore/detail/mhhcjilkgnegleamofnbeacpnblblkhn',
      installUrl: 'https://chrome.google.com/webstore/detail/mhhcjilkgnegleamofnbeacpnblblkhn',
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD',
        availability: 'https://schema.org/InStock',
        category: 'Free'
      },
      description,
      url: canonicalUrl,
      image: options.image,
      author: {
        '@type': 'Organization',
        name: 'Fillify Team',
        url: siteUrl
      },
      publisher: {
        '@type': 'Organization',
        name: 'Fillify Team',
        url: siteUrl,
        logo: {
          '@type': 'ImageObject',
          url: defaultLogo,
          width: 512,
          height: 512
        }
      },
      browserRequirements: 'Requires Chrome Browser version 88 or higher, or Microsoft Edge version 88 or higher',
      softwareVersion: '1.0',
      releaseNotes: 'Initial release with AI-powered form filling capabilities',
      keywords: keywords.join(', '),
      thumbnailUrl: options.image,
      featureList: [
        'AI-powered automatic form filling',
        'Intelligent email composition assistance',
        'Smart bug report generation',
        'Multi-language support (English, Chinese, Spanish, French, Japanese, Korean, Russian)',
        'Multiple AI provider integration (OpenAI, Anthropic Claude, Google Gemini, Moonshot AI)',
        'Custom template creation for bug reports',
        'Real-time form field detection',
        'Privacy-focused local data processing'
      ],
      screenshot: [
        {
          '@type': 'ImageObject',
          url: `${siteUrl}/images/features/form-filling.png`,
          caption: 'AI-powered form filling interface'
        },
        {
          '@type': 'ImageObject',
          url: `${siteUrl}/images/features/email-assistant.png`,
          caption: 'Email composition assistance'
        },
        {
          '@type': 'ImageObject',
          url: `${siteUrl}/images/features/bug-report.png`,
          caption: 'Bug report generation tool'
        }
      ],
      supportingData: {
        '@type': 'DataSet',
        name: 'Fillify Usage Statistics',
        description: 'Performance metrics and user engagement data'
      },
      permissions: 'activeTab, storage',
      memoryRequirements: '50MB',
      processorRequirements: 'Any modern processor',
      storageRequirements: '10MB'
    }

    const scripts: Array<{ type: string; children: string }> = [
      {
        type: 'application/ld+json',
        children: JSON.stringify(schemaOrgData)
      }
    ]

    if (!options.i18nKey) {
      const faqSchema = {
        '@context': 'https://schema.org',
        '@type': 'FAQPage',
        mainEntity: [
          {
            '@type': 'Question',
            name: t('faq.items.what.question'),
            acceptedAnswer: {
              '@type': 'Answer',
              text: t('faq.items.what.answer')
            }
          },
          {
            '@type': 'Question',
            name: t('faq.items.types.question'),
            acceptedAnswer: {
              '@type': 'Answer',
              text: t('faq.items.types.answer')
            }
          },
          {
            '@type': 'Question',
            name: t('faq.items.providers.question'),
            acceptedAnswer: {
              '@type': 'Answer',
              text: t('faq.items.providers.answer')
            }
          },
          {
            '@type': 'Question',
            name: t('faq.items.privacy.question'),
            acceptedAnswer: {
              '@type': 'Answer',
              text: t('faq.items.privacy.answer')
            }
          },
          {
            '@type': 'Question',
            name: t('faq.items.customize.question'),
            acceptedAnswer: {
              '@type': 'Answer',
              text: t('faq.items.customize.answer')
            }
          },
          {
            '@type': 'Question',
            name: t('faq.items.languages.question'),
            acceptedAnswer: {
              '@type': 'Answer',
              text: t('faq.items.languages.answer')
            }
          }
        ]
      }

      scripts.push({
        type: 'application/ld+json',
        children: JSON.stringify(faqSchema)
      })
    }

    if (options.breadcrumbs && options.breadcrumbs.length > 0) {
      const breadcrumbSchema = {
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: options.breadcrumbs.map((crumb, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          name: crumb.name,
          item: crumb.url
        }))
      }

      scripts.push({
        type: 'application/ld+json',
        children: JSON.stringify(breadcrumbSchema)
      })
    }

    if (!options.i18nKey || options.i18nKey === 'about') {
      const organizationSchema = {
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: 'Fillify',
        alternateName: 'Fillify Team',
        url: siteUrl,
        logo: {
          '@type': 'ImageObject',
          url: defaultLogo,
          width: 512,
          height: 512
        },
        image: options.image,
        description: 'Fillify develops AI-powered productivity tools that revolutionize form filling, email composition, and bug report generation.',
        foundingDate: '2024',
        founder: {
          '@type': 'Person',
          name: 'Fillify Team'
        },
        contactPoint: {
          '@type': 'ContactPoint',
          contactType: 'customer service',
          availableLanguage: ['English', 'Chinese', 'Spanish', 'French', 'Japanese', 'Korean', 'Russian']
        },
        sameAs: [
          'https://chrome.google.com/webstore/detail/mhhcjilkgnegleamofnbeacpnblblkhn',
          'https://microsoftedge.microsoft.com/addons/detail/ieedpolbpalhomefmggdickoicpoodab'
        ],
        makesOffer: {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'SoftwareApplication',
            name: 'Fillify Chrome Extension'
          },
          price: '0',
          priceCurrency: 'USD'
        },
        knowsAbout: [
          'Artificial Intelligence',
          'Form Automation',
          'Productivity Software',
          'Browser Extensions',
          'Natural Language Processing'
        ],
        areaServed: 'Worldwide',
        serviceType: 'Software Development'
      }

      scripts.push({
        type: 'application/ld+json',
        children: JSON.stringify(organizationSchema)
      })
    }

    const localeEntries = Array.isArray(locales.value) ? locales.value : []
    const currentLocaleEntry = localeEntries.find((entry: any) => (typeof entry === 'string' ? entry === locale.value : entry.code === locale.value))
    const htmlLang = resolveLanguageTag(currentLocaleEntry) || locale.value
    const ogLocale = resolveLanguageTag(currentLocaleEntry) || 'en_US'

    const alternateLinks: { rel: string; hreflang: string; href: string; key: string }[] = []

    const defaultLocaleCode = typeof defaultLocale?.value === 'string' ? defaultLocale.value : (defaultLocale as any)?.value?.code
    const defaultPath = defaultLocaleCode ? switchLocalePath(defaultLocaleCode) : null
    if (defaultPath) {
      const hrefLang = defaultLocaleCode && typeof currentLocaleEntry !== 'string'
        ? resolveHrefLang(localeEntries.find((entry: any) => (typeof entry === 'string' ? entry === defaultLocaleCode : entry.code === defaultLocaleCode)))
        : defaultLocaleCode || 'en'
      alternateLinks.push({
        rel: 'alternate',
        hreflang: 'x-default',
        href: toAbsoluteUrl(siteUrl, defaultPath),
        key: 'alternate-x-default'
      })
    }

    localeEntries.forEach((entry: any) => {
      const code = typeof entry === 'string' ? entry : entry.code
      if (!code) {
        return
      }

      const hrefLang = resolveHrefLang(entry) || code
      let pathForLocale: string | undefined

      if (code === locale.value) {
        pathForLocale = options.canonicalPath
      } else {
        pathForLocale = switchLocalePath(code) || undefined
      }

      if (!pathForLocale) {
        return
      }

      alternateLinks.push({
        rel: 'alternate',
        hreflang: hrefLang,
        href: toAbsoluteUrl(siteUrl, pathForLocale),
        key: `alternate-${hrefLang}`
      })
    })

    const alternateLocaleMeta = localeEntries
      .filter((entry: any) => (typeof entry === 'string' ? entry !== locale.value : entry.code !== locale.value))
      .map((entry: any) => resolveLanguageTag(entry))
      .filter(Boolean)

    useHead({
      title,
      htmlAttrs: {
        lang: htmlLang
      },
      link: [
        {
          rel: 'canonical',
          href: canonicalUrl,
          key: 'canonical'
        },
        ...alternateLinks
      ],
      meta: [
        {
          property: 'og:image',
          content: options.image,
          key: 'og-image-webp'
        },
        {
          property: 'og:image:width',
          content: '1200',
          key: 'og-image-width'
        },
        {
          property: 'og:image:height',
          content: '630',
          key: 'og-image-height'
        },
        {
          property: 'og:image:alt',
          content: 'Fillify – AI-Powered Automatic Form Filler',
          key: 'og-image-alt'
        },
        {
          property: 'og:image',
          content: defaultImagePng,
          key: 'og-image-png'
        },
        {
          property: 'og:locale',
          content: ogLocale,
          key: 'og-locale'
        },
        ...alternateLocaleMeta.map((altLocale, index) => ({
          property: 'og:locale:alternate',
          content: altLocale,
          key: `og-locale-alt-${index}`
        })),
        {
          name: 'twitter:image',
          content: defaultImagePng,
          key: 'twitter-image'
        },
        {
          name: 'twitter:image:alt',
          content: 'Fillify – AI-Powered Automatic Form Filler',
          key: 'twitter-image-alt'
        }
      ],
      script: scripts
    })

    useSeoMeta({
      title,
      description,
      keywords: keywords.join(', '),
      ogTitle: title,
      ogDescription: description,
      ogUrl: canonicalUrl,
      ogType: 'website',
      ogSiteName: 'Fillify',
      ogImage: options.image,
      twitterTitle: title,
      twitterDescription: description,
      twitterImage: defaultImagePng,
      twitterCard: 'summary_large_image',
      applicationName: 'Fillify',
      author: 'Fillify Team',
      robots: options.robots,
      viewport: 'width=device-width, initial-scale=1'
    })
  }

  watch(locale, () => {
    if (currentOptions.value) {
      updateSeoMeta()
    }
  })

  watch(() => route.path, () => {
    if (currentOptions.value) {
      if (!currentOptions.value.hasExplicitCanonical) {
        currentOptions.value.canonicalPath = withLeadingSlash(route.path ?? '/')
      }
      updateSeoMeta()
    }
  })

  return {
    setSeoMeta
  }
}
