/**
 * Blog structured data composable
 * Provides functions to extract structured data (FAQ, HowTo) from blog content
 */

import { useBlogUtils } from './useBlogUtils'

interface FAQItem {
  question: string
  answer: string
}

interface HowToStep {
  name: string
  text: string
}

interface FAQSchema {
  '@context': string
  '@type': string
  mainEntity: Array<{
    '@type': string
    name: string
    acceptedAnswer: {
      '@type': string
      text: string
    }
  }>
}

interface HowToSchema {
  '@context': string
  '@type': string
  name: string
  description: string
  step: Array<{
    '@type': string
    position: number
    name: string
    text: string
    itemListElement: {
      '@type': string
      text: string
    }
  }>
}

export const useBlogStructuredData = () => {
  const { getTextContent } = useBlogUtils()

  /**
   * Check if heading text contains FAQ-related keywords
   */
  const isFAQHeading = (text: string): boolean => {
    const lowerText = text.toLowerCase()
    return lowerText.includes('faq') ||
           lowerText.includes('frequently asked questions') ||
           lowerText.includes('questions')
  }

  /**
   * Check if text is a question (starts with Q: or ends with ?)
   */
  const isQuestion = (text: string): boolean => {
    const trimmed = text.trim()
    return trimmed.startsWith('Q:') ||
           trimmed.startsWith('**Q:') ||
           trimmed.endsWith('?') ||
           trimmed.endsWith('?**')
  }

  /**
   * Check if heading indicates a how-to/steps section
   */
  const isStepsHeading = (text: string): boolean => {
    const lowerText = text.toLowerCase()
    return lowerText.includes('step') ||
           lowerText.includes('how to') ||
           lowerText.includes('getting started') ||
           lowerText.includes('guide') ||
           /step \d+/.test(lowerText)
  }

  /**
   * Extract FAQ schema from article content for SEO optimization
   */
  const extractFAQSchema = (body: any): FAQSchema | null => {
    if (!body || !body.children) return null

    const faqItems: FAQItem[] = []
    let currentQuestion = ''
    let currentAnswer = ''
    let inFAQSection = false

    const traverseNodes = (nodes: any[]) => {
      for (const node of nodes) {
        if (node.type === 'element') {
          // Check if this is an FAQ section heading
          if ((node.tag === 'h2' || node.tag === 'h3') && node.children) {
            const headingText = getTextContent(node)
            if (isFAQHeading(headingText)) {
              inFAQSection = true
            } else if (inFAQSection && !isQuestion(headingText)) {
              inFAQSection = false
            }
          }

          // If we're in FAQ section, look for Q&A pairs
          if (inFAQSection) {
            const text = getTextContent(node)

            if (isQuestion(text)) {
              // Save previous Q&A if exists
              if (currentQuestion && currentAnswer) {
                faqItems.push({
                  question: currentQuestion.replace(/^\*\*Q:\s*/, '').replace(/Q:\s*/, '').replace(/\*\*/g, '').trim(),
                  answer: currentAnswer.replace(/^\*\*A:\s*/, '').replace(/A:\s*/, '').replace(/\*\*/g, '').trim()
                })
              }
              currentQuestion = text
              currentAnswer = ''
            } else if (currentQuestion && text && !isFAQHeading(text)) {
              const cleanText = text.replace(/^\*\*A:\s*/, '').replace(/A:\s*/, '').trim()
              if (cleanText) {
                currentAnswer += (currentAnswer ? ' ' : '') + cleanText
              }
            }
          }

          // Recurse into children
          if (node.children) {
            traverseNodes(node.children)
          }
        }
      }
    }

    traverseNodes(body.children)

    // Add last Q&A pair
    if (currentQuestion && currentAnswer) {
      faqItems.push({
        question: currentQuestion.replace(/^\*\*Q:\s*/, '').replace(/Q:\s*/, '').replace(/\*\*/g, '').trim(),
        answer: currentAnswer.replace(/^\*\*A:\s*/, '').replace(/A:\s*/, '').replace(/\*\*/g, '').trim()
      })
    }

    if (faqItems.length === 0) return null

    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: faqItems.map(item => ({
        '@type': 'Question',
        name: item.question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: item.answer
        }
      }))
    }
  }

  /**
   * Extract How-To schema from article content for SEO optimization
   */
  const extractHowToSchema = (body: any, title: string, description?: string): HowToSchema | null => {
    if (!body || !body.children) return null

    const steps: HowToStep[] = []
    let inStepsSection = false

    const traverseNodes = (nodes: any[]) => {
      for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i]

        if (node.type === 'element') {
          const text = getTextContent(node)

          // Check for h2/h3 headers that might indicate steps
          if ((node.tag === 'h2' || node.tag === 'h3') && isStepsHeading(text)) {
            inStepsSection = true

            // If the heading itself is a step (e.g., "Step 1: Install")
            if (/step \d+/i.test(text)) {
              const stepContent: string[] = [text]

              // Look ahead to collect content until next step or section
              let j = i + 1
              while (j < nodes.length) {
                const nextNode = nodes[j]
                if (nextNode.type === 'element') {
                  const nextText = getTextContent(nextNode)
                  if ((nextNode.tag === 'h2' || nextNode.tag === 'h3') &&
                      (/step \d+/i.test(nextText) || !isStepsHeading(nextText))) {
                    break
                  }
                  if (nextNode.tag === 'p' || nextNode.tag === 'ul' || nextNode.tag === 'ol') {
                    stepContent.push(nextText)
                  }
                }
                j++
              }

              steps.push({
                name: text.trim(),
                text: stepContent.slice(1).join(' ').trim()
              })
            }
          } else if (inStepsSection && (node.tag === 'h2' || node.tag === 'h3') && !isStepsHeading(text)) {
            inStepsSection = false
          }

          // Also check for ordered lists (ol) which often contain steps
          if (node.tag === 'ol' && node.children) {
            let stepNum = 1
            for (const child of node.children) {
              if (child.tag === 'li') {
                const stepText = getTextContent(child)
                if (stepText) {
                  steps.push({
                    name: `Step ${stepNum}`,
                    text: stepText.trim()
                  })
                  stepNum++
                }
              }
            }
          }

          // Recurse into children (except ol to avoid duplication)
          if (node.children && node.tag !== 'ol') {
            traverseNodes(node.children)
          }
        }
      }
    }

    traverseNodes(body.children)

    if (steps.length === 0) return null

    return {
      '@context': 'https://schema.org',
      '@type': 'HowTo',
      name: title,
      description: description || title,
      step: steps.map((step, index) => ({
        '@type': 'HowToStep',
        position: index + 1,
        name: step.name,
        text: step.text,
        itemListElement: {
          '@type': 'HowToDirection',
          text: step.text
        }
      }))
    }
  }

  return {
    extractFAQSchema,
    extractHowToSchema
  }
}
